#include "include.h"
float 骨骼高度 PovWrite 方框调整;
void 获取进程信息()
{
    pid_t game_pid = 驱动->获取进程ID("com.tencent.mf.uam");
    if (game_pid <= 0)
    {
        cout << "\n游戏未运行" << endl;
        exit(0);
    }
    驱动->init_key("0xDFFDABCD03007677");
    驱动->初始化进程(game_pid);
    libbase = 驱动->get_module_base("libUE4.so");
    if (libbase == 0)
    {
        cout << "\n驱动链接错误请重启或者换驱动" << endl;
        exit(0);
    }
}

void 再次获取进程信息()
{
    pid_t game_pid = 驱动->获取进程ID("com.tencent.mf.uam");
    驱动->初始化进程(game_pid);
    libbase = 驱动->获取基址头("libUE4.so");
}

void 绘图加载(ImDrawList *draw)
{
    GName = libbase + 0xf110ac0;

    Uworld = 驱动->读取指针(libbase + 0xf336080);
    Uleve = 驱动->读取指针(Uworld + 0x30);
    //    Arrayaddr = 驱动->读取指针(Uleve + 0x98);
    Count = 驱动->读取整数(Uleve + 0xA0);
    long HumanActor = 驱动->读取指针(驱动->读取指针(驱动->读取指针(Uworld + 0x718) + 0x2F0) + 0x48);
    int8_t HumanCount = driver->read<int8_t>(驱动->读取指针(驱动->读取指针(Uworld + 0x718) + 0x2F0) + 0x50);
    GameInstance = 驱动->读取指针(Uworld + 0x180);
    LocalPlayers = 驱动->读取指针(GameInstance + 0x38);
    Player = 驱动->读取指针(LocalPlayers + 0x0);
    PlayerController = 驱动->读取指针(Player + 0x30);
    MySelf = 驱动->读取指针(PlayerController + 0x380);
    uint64_t MMesh = 驱动->读取指针(MySelf + 0x370);
    uint64_t Mhuman = 驱动->读取指针(MMesh + 0x200);
    uint64_t MBone = 驱动->读取指针(MMesh + 0x648);
    if (!MBone)
        uint64_t Bone = 驱动->读取指针(MMesh + 0x648 + 0x10);
    Vector3A TruePP = GetVector3Pos(MBone, Mhuman);
    Myteam = 驱动->读取整数(驱动->读取指针(MySelf + 0x330) + 0x558);
    uint64_t 游戏实例 = 驱动->读取指针(驱动->读取指针(libbase + 0xf336080) + 0x180);
    uint64_t 本地播放器 = 驱动->读取指针(游戏实例 + 0x38);
    uint64_t 游戏者 = 驱动->读取指针(本地播放器 + 0);
    uint64_t 玩家控制器 = 驱动->读取指针(游戏者 + 0x30);
    uint64_t 玩家摄像机管理器 = 驱动->读取指针(玩家控制器 + 0x398);
    POV = 驱动->read<MinimalViewInfo>(驱动->读取指针(PlayerCameraManager + 0x158) + 0x154); //
    POV.FOV = 驱动->read<float>(PlayerCameraManager + 0x10D0 + 0x10 + 0x18);
    驱动->read(驱动->读取指针(MySelf + 0x158) + 0x154, &自身坐标, sizeof(自身坐标));
    POV.FOV += PovWrite;
    memset(matrix, 0, 16);
    驱动->read(Matrix, matrix, 16 * 4);

    PlayerCounts = 0;
    BotCounts = 0;

    for (int i = 0; i < HumanCount + 1; i++)
    {
        Objaddr = 驱动->读取指针(HumanActor + 0x8 * i);

        IsBot = 驱动->读取指针(Objaddr + 0x330);
        if (!IsBot)
        {
            continue;
        }
        if (坐标切换 == 1)
        {
            Pos = 驱动->read<Vector3A>(驱动->读取指针(驱动->读取指针(驱动->读取指针(驱动->读取指针(Objaddr + 0x370) + 0x578) + 0x1f0) + 0x50) + 0x10) + 0x30);
        }

        else if (坐标切换 == 0)
        {
            Pos = 驱动->read<Vector3A>(驱动->读取指针(Objaddr + 0x158) + 0x154);
        }

        if (Pos.X == 0.0 && Pos.Y == 0.0 && Pos.Z == 0.0)
        {
            continue;
        }

        ProblemPos = 驱动->read<Vector3A>(驱动->读取指针(Objaddr + 0x158) + 0x154);
        if (ProblemPos != Pos && Pos != (struct Vector3A){
                                            0, 0, 0})
            PosMap[Objaddr] = Pos - ProblemPos;
        if (PosMap.count(Objaddr))
            ProblemPos += PosMap[Objaddr];
        Pos = ProblemPos;
        Pos.Z += 方框调整;
        Pos.Z += 185;
        auto ScreenPos1 = WorldToScreen2(Pos, POV);
        Pos.Z -= 190;
        auto ScreenPos2 = WorldToScreen2(Pos, POV);

        if (Objaddr == MySelf)
        {
            continue;
        }

        float W = (ScreenPos2.Y - ScreenPos1.Y) / 2;
        float X = ScreenPos1.X - W / 2;
        float Y = ScreenPos1.Y + W;
        MIDDLE = X + W / 2;
        BOTTOM = Y + W;
        TOP = Y - W;

        if (驱动->读取浮点数(Objaddr + 0x42C) != 40.0f)
        {
            continue;
        }

        Distance = sqrt(pow(敌人坐标.X - 自身坐标.X, 2) + pow(敌人坐标.Y - 自身坐标.Y, 2) + pow(敌人坐标.Z - 自身坐标.Z, 2)) * 0.01;

        TeamID = 驱动->读取整数(IsBot + 0x560);
        读取字符(PlayerName, 驱动->读取指针(IsBot + 0x440));

        /*if (!绘制队友)
        {
            if(Myteam == TeamID)
            {
                continue;
            }
        }
        */
        Mesh = 驱动->读取指针(Objaddr + 0x370);
        human = 驱动->读取指针(Mesh + 0x200);
        Bone = 驱动->读取指针(Mesh + 0x648);

        if (Bone == 0)
        {
            continue;
        }

        if (W > 0)
        {
            if (绘制射线)
            {
                draw->AddLine(
                    {((float)屏幕X / 2), 0},
                    {MIDDLE, TOP}, 黄色,
                    {1.5});
            }
            /*
            if (绘制方框)
            {
                draw->AddLine(
                {
                    X, TOP
                }
                ,
                {
                    X+(W/3), TOP
                }
                , 黄色, 2.5f);
                draw->AddLine(
                {
                    X+W, TOP
                }
                ,
                {
                    X+W-(W/3), TOP
                }
                ,黄色, 2.5f);
                draw->AddLine(
                {
                    X, TOP
                }
                ,
                {
                    X, TOP+(W/3)
                }
                ,黄色, 2.5f);
                draw->AddLine(
                {
                    X+W, TOP
                }
                ,
                {
                    X+W, TOP+(W/3)
                }
                ,黄色, 2.5f);
                draw->AddLine(
                {
                    X, BOTTOM
                }
                ,
                {
                    X+(W/3), BOTTOM
                }
                ,黄色, 2.5f);
                draw->AddLine(
                {
                    X+W, BOTTOM
                }
                ,
                {
                    X+W-(W/3), BOTTOM
                }
                ,黄色, 2.5f);
                draw->AddLine(
                {
                    X, BOTTOM
                }
                ,
                {
                    X, BOTTOM-(W/3)
                }
                ,黄色, 2.5f);
                draw->AddLine(
                {
                    X+W, BOTTOM
                }
                ,
                {
                    X+W, BOTTOM-(W/3)
                }
                ,黄色, 2.5f);
            }
            */
            if (绘制骨骼)
            {
                Mesh = 驱动->读取指针(Objaddr + 0x370);
                human = 驱动->读取指针(Mesh + 0x200);
                Bone = 驱动->读取指针(Mesh + 0x648);

                FTransform meshtrans = 驱动->read<FTransform>(human);
                meshtrans.Translation =
                    Pos - (驱动->read<Vector3A>(驱动->读取指针(Objaddr + 0x158) + 0x154) -
                           meshtrans.Translation);
                FMatrix c2wMatrix = TransformToMatrix(meshtrans);

                // 头部
                FTransform headtrans = 驱动->read<FTransform>(Bone + 82 * 48);
                FMatrix boneMatrix = TransformToMatrix(headtrans);
                Vector3A relLocation =
                    MatrixToVector(MatrixMulti(boneMatrix, c2wMatrix));
                relLocation.Z += 7; // 脖子长度
                Head = WorldToScreen2(relLocation, POV);
                FTransform chesttrans = 驱动->read<FTransform>(Bone + 15 * 48);
                FMatrix boneMatrix1 = TransformToMatrix(chesttrans);
                Vector3A relLocation1 =
                    MatrixToVector(MatrixMulti(boneMatrix1, c2wMatrix));
                Vector2A Chest = WorldToScreen2(relLocation1, POV);
                // Vector2A Chest = WorldToScreen(relLocation1, POV);
                // 盆骨
                FTransform pelvistrans = 驱动->read<FTransform>(Bone + 1 * 48);
                FMatrix boneMatrix2 = TransformToMatrix(pelvistrans);
                Vector3A LrelLocation1 =
                    MatrixToVector(MatrixMulti(boneMatrix2, c2wMatrix));
                Vector2A Pelvis = WorldToScreen2(LrelLocation1, POV);
                // 左肩膀
                FTransform lshtrans = 驱动->read<FTransform>(Bone + 53 * 48);
                FMatrix boneMatrix3 = TransformToMatrix(lshtrans);
                Vector3A relLocation2 =
                    MatrixToVector(MatrixMulti(boneMatrix3, c2wMatrix));
                Vector2A Left_Shoulder = WorldToScreen2(relLocation2, POV);
                // 右肩膀
                FTransform rshtrans = 驱动->read<FTransform>(Bone + 23 * 48);
                FMatrix boneMatrix4 = TransformToMatrix(rshtrans);
                Vector3A relLocation3 =
                    MatrixToVector(MatrixMulti(boneMatrix4, c2wMatrix));
                Vector2A Right_Shoulder = WorldToScreen2(relLocation3, POV);
                // 左手肘
                FTransform lelbtrans = 驱动->read<FTransform>(Bone + 54 * 48);
                FMatrix boneMatrix5 = TransformToMatrix(lelbtrans);
                Vector3A relLocation4 =
                    MatrixToVector(MatrixMulti(boneMatrix5, c2wMatrix));
                Vector2A Left_Elbow = WorldToScreen2(relLocation4, POV);
                // 右手肘
                FTransform relbtrans = 驱动->read<FTransform>(Bone + 24 * 48);
                FMatrix boneMatrix6 = TransformToMatrix(relbtrans);
                Vector3A relLocation5 =
                    MatrixToVector(MatrixMulti(boneMatrix6, c2wMatrix));
                Vector2A Right_Elbow = WorldToScreen2(relLocation5, POV);
                // 左手腕
                FTransform lwtrans = 驱动->read<FTransform>(Bone + 87 * 48);
                FMatrix boneMatrix7 = TransformToMatrix(lwtrans);
                Vector3A relLocation6 =
                    MatrixToVector(MatrixMulti(boneMatrix7, c2wMatrix));
                Vector2A Left_Wrist = WorldToScreen2(relLocation6, POV);
                // 右手腕
                FTransform rwtrans = 驱动->read<FTransform>(Bone + 86 * 48);
                FMatrix boneMatrix8 = TransformToMatrix(rwtrans);
                Vector3A relLocation7 =
                    MatrixToVector(MatrixMulti(boneMatrix8, c2wMatrix));
                Vector2A Right_Wrist = WorldToScreen2(relLocation7, POV);
                // 左大腿
                FTransform Llshtrans = 驱动->read<FTransform>(Bone + 93 * 48);
                FMatrix boneMatrix9 = TransformToMatrix(Llshtrans);
                Vector3A LrelLocation2 =
                    MatrixToVector(MatrixMulti(boneMatrix9, c2wMatrix));
                Vector2A Left_Thigh = WorldToScreen2(LrelLocation2, POV);
                // 右大腿
                FTransform Lrshtrans = 驱动->read<FTransform>(Bone + 95 * 48);
                FMatrix boneMatrix10 = TransformToMatrix(Lrshtrans);
                Vector3A LrelLocation3 =
                    MatrixToVector(MatrixMulti(boneMatrix10, c2wMatrix));
                Vector2A Right_Thigh = WorldToScreen2(LrelLocation3, POV);
                // 左膝盖
                FTransform Llelbtrans = 驱动->read<FTransform>(Bone + 93 * 48);
                FMatrix boneMatrix11 = TransformToMatrix(Llelbtrans);
                Vector3A LrelLocation4 =
                    MatrixToVector(MatrixMulti(boneMatrix11, c2wMatrix));
                Vector2A Left_Knee = WorldToScreen2(LrelLocation4, POV);
                // 右膝盖
                FTransform Lrelbtrans = 驱动->read<FTransform>(Bone + 95 * 48);
                FMatrix boneMatrix12 = TransformToMatrix(Lrelbtrans);
                Vector3A LrelLocation5 =
                    MatrixToVector(MatrixMulti(boneMatrix12, c2wMatrix));
                Vector2A Right_Knee = WorldToScreen2(LrelLocation5, POV);
                // 左脚腕
                FTransform Llwtrans = 驱动->read<FTransform>(Bone + 89 * 48);
                FMatrix boneMatrix13 = TransformToMatrix(Llwtrans);
                Vector3A LrelLocation6 =
                    MatrixToVector(MatrixMulti(boneMatrix13, c2wMatrix));
                Vector2A Left_Ankle = WorldToScreen2(LrelLocation6, POV);
                // 右脚腕
                FTransform Lrwtrans = 驱动->read<FTransform>(Bone + 90 * 48);
                FMatrix boneMatrix14 = TransformToMatrix(Lrwtrans);
                Vector3A LrelLocation7 =
                    MatrixToVector(MatrixMulti(boneMatrix14, c2wMatrix));
                Vector2A Right_Ankle = WorldToScreen2(LrelLocation7, POV);

                draw->AddCircle(
                    {Head.X, Head.Y}, W / 10, 黄色, 0);
                draw->AddLine(
                    {Head.X, Head.Y},
                    {Chest.X, Chest.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Chest.X, Chest.Y},
                    {Pelvis.X, Pelvis.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Chest.X, Chest.Y},
                    {Left_Shoulder.X, Left_Shoulder.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Chest.X, Chest.Y},
                    {Right_Shoulder.X, Right_Shoulder.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Left_Shoulder.X, Left_Shoulder.Y},
                    {Left_Elbow.X, Left_Elbow.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Right_Shoulder.X, Right_Shoulder.Y},
                    {Right_Elbow.X, Right_Elbow.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Left_Elbow.X, Left_Elbow.Y},
                    {Left_Wrist.X, Left_Wrist.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Right_Elbow.X, Right_Elbow.Y},
                    {Right_Wrist.X, Right_Wrist.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Pelvis.X, Pelvis.Y},
                    {Left_Thigh.X, Left_Thigh.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Pelvis.X, Pelvis.Y},
                    {Right_Thigh.X, Right_Thigh.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Left_Thigh.X, Left_Thigh.Y},
                    {Left_Knee.X, Left_Knee.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Right_Thigh.X, Right_Thigh.Y},
                    {Right_Knee.X, Right_Knee.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Left_Knee.X, Left_Knee.Y},
                    {Left_Ankle.X, Left_Ankle.Y}, 黄色,
                    {1.5});
                draw->AddLine(
                    {Right_Knee.X, Right_Knee.Y},
                    {Right_Ankle.X, Right_Ankle.Y}, 黄色,
                    {1.5});
            }
            //}

            // if (TeamID != Myteam) {
            if (绘制信息)
            {
                SGCharacterWeaponManagerComponent = 驱动->读取指针(Objaddr + 0x1730);
                CurrentWeapon = 驱动->读取指针(SGCharacterWeaponManagerComponent + 0x138);

                SGInventoryCommonData = 驱动->读取指针(CurrentWeapon + 0xa20);
                DisplayName = 驱动->读取指针(驱动->读取指针(驱动->读取指针(SGInventoryCommonData + 0x138) + 8) + 0);
                读取字符(手持, DisplayName);

                CharacterInventoryManagerComponent = 驱动->读取指针(Objaddr + 0x1740);
                负重 = 驱动->读取浮点数(CharacterInventoryManagerComponent + 0x1E8);
                价值 = 驱动->读取整数(CharacterInventoryManagerComponent + 0x1EC);
                stringstream 负重stream;
                负重stream << fixed << setprecision(1) << 负重;
                负重字符串 = 负重stream.str();
                价值约数 = 价值 / 10000;
                stringstream 价值stream;
                价值stream << fixed << setprecision(1) << 价值约数;
                价值字符串 = 价值stream.str();
                CharacterArmorManagerComponent = 驱动->读取指针(Objaddr + 0x1768);
                ProtectiveArmorList = 驱动->读取指针(CharacterArmorManagerComponent + 0x268);
                头 = 驱动->读取整数(驱动->读取指针(ProtectiveArmorList) + 0x6B0);
                甲 = 驱动->读取整数(驱动->读取指针(ProtectiveArmorList + 0x8) + 0x6B0);
                if (头 <= 0 || 头 >= 7)
                    头 = 0;
                if (甲 <= 0 || 甲 >= 7)
                    甲 = 0;

                if (显热成像)
                {
                    InventoryArray = 驱动->读取指针(Objaddr + 0x1740) + 0xF8;
                    最大循环次数 = 驱动->读取整数(InventoryArray + 0x8);
                    for (int 循环次数 = 0; 循环次数 < 最大循环次数 + 1; 循环次数++)
                    {
                        InventoryGet = 驱动->读取指针(驱动->读取指针(InventoryArray) + 0x8 * 循环次数);
                        库存ID = 驱动->读取整数(InventoryGet + 0x18);
                        GNames = 获取类名(GName, 库存ID);
                        if (GNames.find("ThermalImagerBase_Child_C") != string::npos)
                        {
                            热成像判断 = 1;
                            break;
                        }
                    }
                }

                PlayerInfo = "";
                PlayerInfo += 负重字符串;
                PlayerInfo += "KG ";
                PlayerInfo += 价值字符串;
                PlayerInfo += "万";
                PlayerInfo += "\n";
                PlayerInfo += to_string(头);
                PlayerInfo += "头";
                PlayerInfo += " ";
                PlayerInfo += to_string(甲);
                PlayerInfo += "甲";
                if (热成像判断 == 1)
                {
                    PlayerInfo += " <T7热成像>";
                    热成像判断 = 0;
                }
                /*    PlayerInfo += “\n“;
                    PlayerInfo += to_string((int)Distance);
                    PlayerInfo += “米“;*/
                auto Size = ImGui::CountTextSize(NULL, PlayerInfo.c_str(), 15.f);
                draw->AddText(NULL, 28.f,
                              {MIDDLE - (Size.x / 2), BOTTOM}, 白色, PlayerInfo.c_str());

                PlayerInfo = "";
                PlayerInfo += to_string((int)TeamID);
                PlayerInfo += ".";
                PlayerInfo += PlayerName;
                PlayerInfo += "\n";
                PlayerInfo += 手持;
                Size = ImGui::CountTextSize(NULL, PlayerInfo.c_str(), 15.f);
                draw->AddText(NULL, 28.f,
                              {MIDDLE - (Size.x / 2), TOP - (Size.y * 2)}, 白色, PlayerInfo.c_str());
            }
            /*//}
            else
            {
                if (绘制队友)
                {
                    if (绘制信息)
                    {
                        PlayerInfo = "";
                        PlayerInfo += PlayerName;
                        auto Size = ImGui::CountTextSize(NULL, PlayerInfo.c_str(), 15.f);
                        draw->AddText(NULL, 28.f,
                        {
                            MIDDLE - (Size.x / 2), TOP - (Size.y * 2)
                        }
                        , 红色, PlayerInfo.c_str());
                    }
                }
            }
            */
        }
        PlayerCounts++;
        BotCounts++;
    }

    // 红色 = ImColor(256, 0, 0);
    // 绿色 = ImColor(0, 256, 0);

    if (绘制世界)
    {
        for (int i = 0; i < Count + 1; i++)
        {
            Objaddr = 驱动->读取指针(Arrayaddr + 0x8 * i);

            Pos = 驱动->read<Vector3A>(驱动->读取指针(Objaddr + 0x158) + 0x154);

            if (Pos.X == 0.0 && Pos.Y == 0.0 && Pos.Z == 0.0)
            {
                continue;
            }

            ProblemPos = 驱动->read<Vector3A>(驱动->读取指针(Objaddr + 0x158) + 0x154);
            if (ProblemPos != Pos && Pos != (struct Vector3A){
                                                0, 0, 0})
                PosMap[Objaddr] = Pos - ProblemPos;
            if (PosMap.count(Objaddr))
                ProblemPos += PosMap[Objaddr];
            Pos = ProblemPos;
            Pos.Z += 185;
            auto ScreenPos1 = WorldToScreen2(Pos, POV);
            Pos.Z -= 190;
            auto ScreenPos2 = WorldToScreen2(Pos, POV);

            float W = (ScreenPos2.Y - ScreenPos1.Y) / 2;
            float X = ScreenPos1.X - W / 2;
            float Y = ScreenPos1.Y + W;
            if (W > 0)
            {

                if (绘制类名)
                {
                    NameID = 驱动->读取整数(Objaddr + 0x18);
                    GNames = 获取类名(GName, NameID);
                    draw->AddText(NULL, 28,
                                  {X, Y}, 红色, GNames.c_str());
                }

                if (绘制盒子)
                {
                    MaxEffectTime = 驱动->读取浮点数(Objaddr + 0x898);
                    if (MaxEffectTime == 3.0f)
                    {
                        InventoryTypeComponent = 驱动->读取指针(Objaddr + 0x798);
                        DeadCharacterType = 驱动->读取短字符(InventoryTypeComponent + 0x118);
                        if (DeadCharacterType == ECharacterType_PMC)
                        {
                            draw->AddText(NULL, 28,
                                          {X, Y}, 黄色, "真人盒");
                        }
                        else if (DeadCharacterType == ECharacterType_SCAV)
                        {
                            draw->AddText(NULL, 28,
                                          {X, Y}, 黄色, "伪装盒");
                        }
                        else if (DeadCharacterType == ECharacterType_AI_SCAV)
                        {
                            draw->AddText(NULL, 28,
                                          {X, Y}, 黄色, "人机盒");
                        }
                        else if (DeadCharacterType == ECharacterType_AI_SCAV_BOSS)
                        {
                            draw->AddText(NULL, 28,
                                          {X, Y}, 黄色, "首领盒");
                        }
                        else if (DeadCharacterType == ECharacterType_AI_PMC)
                        {
                            draw->AddText(NULL, 28,
                                          {X, Y}, 黄色, "特遣盒");
                        }
                        else if (DeadCharacterType == ECharacterType_AI_ELIT)
                        {
                            draw->AddText(NULL, 28,
                                          {X, Y}, 黄色, "精英盒");
                        }
                        else if (DeadCharacterType == ECharacterType_BOSS)
                        {
                            draw->AddText(NULL, 28,
                                          {X, Y}, 黄色, "BOSS盒");
                        }
                        else if (DeadCharacterType == ECharacterType_AI_SCAV_Follower)
                        {
                            draw->AddText(NULL, 28,
                                          {X, Y}, 黄色, "男团盒");
                        }
                        else
                        {
                            draw->AddText(NULL, 28,
                                          {X, Y}, 黄色, "未知盒");
                        }
                    }
                }
            }
        }
    }

    if (绘制人数)
    {
        sprintf(extras, "玩家 : %d", PlayerCounts);
        绘制字体描边(60.0f, (屏幕X / 2) - 180, 80.0f, ImColor(255, 255, 255), extras);
        sprintf(extra, "人机 : %d", BotCounts);
        绘制字体描边(60.0f, (屏幕X / 2) + 40, 80.0f, ImColor(255, 255, 255), extra);
    }

    if (绘制准心)
    {
        draw->AddLine(
            {(float)屏幕X / 2 + 25, (float)屏幕Y / 2},
            {(float)屏幕X / 2 - 25, (float)屏幕Y / 2}, ImColor(205, 50, 0), 2);
        draw->AddLine(
            {(float)屏幕X / 2, (float)屏幕Y / 2 + 25},
            {(float)屏幕X / 2, (float)屏幕Y / 2 - 25}, ImColor(205, 50, 0), 2);
    }

    // string guistr = “@STOPnb666“;
    string guistr = "FPS";
    guistr += to_string((int)ImGui::GetIO().Framerate);
    draw->AddText(NULL, 42,
                  {150, 85}, ImColor(255, 255, 0), guistr.c_str());
}