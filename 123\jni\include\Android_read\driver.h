#include <algorithm>
#include <arpa/inet.h>
#include <cctype>
#include <chrono>
#include <codecvt>
#include <cstdlib>
#include <ctime>
#include <deque>
#include <dirent.h>
#include <dlfcn.h>
#include <errno.h>
#include <fcntl.h>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <locale>
#include <malloc.h>
#include <map>
#include <math.h>
#include <netdb.h>
#include <netinet/in.h>
#include <pthread.h>
#include <regex.h>
#include <sstream>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <string.h>
#include <sys/fcntl.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/syscall.h>
#include <sys/sysmacros.h>
#include <sys/types.h>
#include <sys/uio.h>
#include <sys/user.h>
#include <sys/wait.h>
#include <thread>
#include <unistd.h>
#include <vector>


class c_driver {
private:
	int has_upper = 0;
	int has_lower = 0;
	int has_symbol = 0;
	int has_digit = 0;
	int fd;
	pid_t pid;

	typedef struct _COPY_MEMORY {
		pid_t pid;
		uintptr_t addr;
		void* buffer;
		size_t size;
	} COPY_MEMORY, * PCOPY_MEMORY;

	typedef struct _MODULE_BASE {
		pid_t pid;
		char* name;
		uintptr_t base;
	} MODULE_BASE, * PMODULE_BASE;

	struct process {
		pid_t process_pid;
		char process_comm[15];
	};

	enum OPERATIONS {
		OP_INIT_KEY = 0x800,
		OP_READ_MEM = 0x801,
		OP_WRITE_MEM = 0x802,
		OP_MODULE_BASE = 0x803,
		OP_HIDE_PROCESS = 0x804,
		OP_PID_HIDE_PROCESS = 0x805,
		OP_GET_PROCESS_PID = 0x806
	};

	int symbol_file(const char* filename) {
		//判断文件名是否含小写并且不含大写不含数字不含符号
		int length = strlen(filename);
		for (int i = 0; i < length; i++) {
			if (islower(filename[i])) {
				has_lower = 1;
			}
			else if (isupper(filename[i])) {
				has_upper = 1;
			}
			else if (ispunct(filename[i])) {
				has_symbol = 1;
			}
			else if (isdigit(filename[i])) {
				has_digit = 1;
			}
		}
		return has_lower && !has_upper && !has_symbol && !has_digit;
	}
	char* qx8() {
		// 打开目录
		const char* dev_path = "/dev";
		DIR* dir = opendir(dev_path);
		if (dir == NULL) {
			printf("无法打开/dev目录\n");
			return NULL;
		}

		char* files[] = { "wanbai", "CheckMe", "Ckanri", "lanran","video188" };
		struct dirent* entry;
		char* file_path = NULL;
		while ((entry = readdir(dir)) != NULL) {
			// 跳过当前目录和上级目录
			if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
				continue;
			}

			size_t path_length = strlen(dev_path) + strlen(entry->d_name) + 2;
			file_path = (char*)malloc(path_length);
			snprintf(file_path, path_length, "%s/%s", dev_path, entry->d_name);
			for (int i = 0; i < 5; i++) {
				if (strcmp(entry->d_name, files[i]) == 0) {
					printf("驱动文件：%s\n", file_path);
					closedir(dir);
					return file_path;
				}
			}

			// 获取文件stat结构
			struct stat file_info;
			if (stat(file_path, &file_info) < 0) {
				free(file_path);
				file_path = NULL;
				continue;
			}

			// 跳过gpio接口
			if (strstr(entry->d_name, "gpiochip") != NULL) {
				free(file_path);
				file_path = NULL;
				continue;
			}

			// 检查是否为驱动文件
			if ((S_ISCHR(file_info.st_mode) || S_ISBLK(file_info.st_mode))
				&& strchr(entry->d_name, '_') == NULL && strchr(entry->d_name, '-') == NULL && strchr(entry->d_name, ':') == NULL) {
				// 过滤标准输入输出
				if (strcmp(entry->d_name, "stdin") == 0 || strcmp(entry->d_name, "stdout") == 0
					|| strcmp(entry->d_name, "stderr") == 0) {
					free(file_path);
					file_path = NULL;
					continue;
				}

				size_t file_name_length = strlen(entry->d_name);
				time_t current_time;
				time(&current_time);
				int current_year = localtime(&current_time)->tm_year + 1900;
				int file_year = localtime(&file_info.st_ctime)->tm_year + 1900;
				//跳过1980年前的文件
				if (file_year <= 1980) {
					free(file_path);
					file_path = NULL;
					continue;
				}

				time_t atime = file_info.st_atime;
				time_t ctime = file_info.st_ctime;
				// 检查最近访问时间和修改时间是否一致并且文件名是否是symbol文件
				if ((atime == ctime)/* && symbol_file(entry->d_name)*/) {
					//检查mode权限类型是否为S_IFREG(普通文件)和大小还有gid和uid是否为0(root)并且文件名称长度在7位或7位以下
					if ((file_info.st_mode & S_IFMT) == 8192 && file_info.st_size == 0
						&& file_info.st_gid == 0 && file_info.st_uid == 0 && file_name_length <= 9) {
						printf("驱动文件：%s\n", file_path);
						closedir(dir);
						return file_path;
					}
				}
			}
			free(file_path);
			file_path = NULL;
		}
		closedir(dir);
		return NULL;
	}
	char* qx10()
	{
		const char* command = "dir=$(ls -l /proc/*/exe 2>/dev/null | grep -E '/data/[^/]* \\(deleted\\)' | sed 's/ /\\n/g' | grep '/proc' | sed 's/\\/[^/]*$//g');if [[ \"$dir\" ]]; then sbwj=$(head -n 1 \"$dir/comm\");open_file=\"\";for file in \"$dir\"/fd/*; do link=$(readlink \"$file\");if [[ \"$link\" == \"/dev/$sbwj (deleted)\" ]]; then open_file=\"$file\";break;fi;done;if [[ -n \"$open_file\" ]]; then nhjd=$(echo \"$open_file\");sbid=$(ls -L -l \"$nhjd\" | sed 's/\\([^,]*\\).*/\\1/' | sed 's/.*root //');echo \"/dev/$sbwj\";rm -rf \"/dev/$sbwj\";mknod \"/dev/$sbwj\" c \"$sbid\" 0;fi;fi;";
		FILE* file = popen(command, "r");
		if (file == NULL) {
			return NULL;
		}
		static char result[512];
		if (fgets(result, sizeof(result), file) == NULL) {
			return NULL;
		}
		pclose(file);
		result[strlen(result) - 1] = '\0';
		return result;
	}
	char* xgh()
	{
		const char* dev_path = "/proc";
		DIR* dir = opendir(dev_path);
		if (dir == NULL)
		{
			printf("open /proc failed\n");
			return NULL;
		}
		struct dirent* entry;
		static char file_path[256];
		while ((entry = readdir(dir)) != NULL)
		{
			if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0)
			{
				continue;
			}
			sprintf(file_path, "%s/%s", dev_path, entry->d_name);
			struct stat file_info;
			if (stat(file_path, &file_info) < 0)
			{
				continue;
			}
			size_t file_name_length = strlen(entry->d_name);
			if ((file_info.st_mode & S_IFMT) == S_IFREG && file_info.st_size == 0)
			{
				if (file_info.st_blksize == 1024 && file_info.st_nlink == 1 &&
					(file_info.st_mode & S_IRUSR) == S_IRUSR && file_info.st_uid == 0 &&
					(file_info.st_mode &
						(S_IRGRP | S_IWGRP | S_IXGRP | S_IROTH | S_IWOTH | S_IXOTH)) == 0
					&& file_info.st_gid == 0)
				{
					if (file_info.st_atime == file_info.st_ctime && file_name_length == 6)
					{
						closedir(dir);
						// printf("path:%s\n", file_path);
						return strdup(file_path);
					}
				}
			}
		}
		closedir(dir);
		return NULL;

	}

	int open_driver() {
		char chose[20];
		FILE* fp;
		char buffer[1024];
		fp = popen("uname -a", "r");
		while (fgets(buffer, sizeof(buffer), fp) != NULL) {
			printf("%s", buffer);
		}
		pclose(fp);
        printf("\033[91m");
		printf("\n[1]Qx8.2\n[2]Gt/Rh\n[3]Proc");
		printf("\n请选择你的对接方式1/2/3:");
		scanf("%s", chose);
		int choose = atoi(chose);
		switch (choose)
		{
		case 1: {
			char* dev_path3 = qx10();
			if (dev_path3 != NULL) {
				fd = open(dev_path3, O_RDWR);
				printf("隐藏驱动：%s\n", dev_path3);
				unlink(dev_path3);
				return 1;
			}
			break;
		}
		case 2: {
			char* dev_path2 = qx8();
			if (dev_path2 != NULL) {
				fd = open(dev_path2, O_RDWR);
				printf("隐藏驱动：%s\n", dev_path2);
				unlink(dev_path2);
				return 1;

			}
			break;
		}
		case 3:
		{
			char* dev_path1 = xgh();
			if (dev_path1 != NULL) {
				fd = open(dev_path1, O_RDWR);
				printf("隐藏驱动：%s\n", dev_path1);
				unlink(dev_path1);


				return 1;
			}
			break;
		}
		default:
			printf("\n草拟马，让你输1-3看不明白吗");
			exit(0);
			return 0;//1

		}
	}
public:
	c_driver() {
		open_driver();
		if (fd <= 0) {
			printf("[-] open driver failed\n");
			exit(0);
		}
	}

	~c_driver()
	{
		if (fd > 0)
			close(fd);
	}


	void initialize(pid_t pid) {
		this->pid = pid;
	}

	bool init_key(char* key) {
		char buf[0x100];
		strcpy(buf, key);
		if (ioctl(fd, OP_INIT_KEY, buf) != 0) {
			return false;
		}
		return true;
	}

	bool read(uintptr_t addr, void* buffer, size_t size) {
		COPY_MEMORY cm;

		cm.pid = this->pid;
		cm.addr = addr;
		cm.buffer = buffer;
		cm.size = size;

		if (ioctl(fd, OP_READ_MEM, &cm) != 0) {
			return false;
		}
		return true;
	}

	bool write(uintptr_t addr, void* buffer, size_t size) {
		COPY_MEMORY cm;

		cm.pid = this->pid;
		cm.addr = addr;
		cm.buffer = buffer;
		cm.size = size;

		if (ioctl(fd, OP_WRITE_MEM, &cm) != 0) {
			return false;
		}
		return true;
	}

	template <typename T>
	T read(uintptr_t addr) {
		T res;
		if (this->read(addr, &res, sizeof(T)))
			return res;
		return {};
	}

	template <typename T>
	bool write(uintptr_t addr, T value) {
		return this->write(addr, &value, sizeof(T));
	}

	uintptr_t get_module_base(char* name) {
		MODULE_BASE mb;
		char buf[0x100];
		strcpy(buf, name);
		mb.pid = this->pid;
		mb.name = buf;

		if (ioctl(fd, OP_MODULE_BASE, &mb) != 0) {
			return 0;
		}
		return mb.base;
	}

	pid_t Get_Name_Pid(char* name)
	{
		FILE* fp;
		pid_t pid;
		char cmd[0x100] = "pidof ";

		strcat(cmd, name);
		fp = popen(cmd, "r");
		fscanf(fp, "%d", &pid);
		pclose(fp);
		return pid;
	}
};

static std::unique_ptr<c_driver> driver = std::make_unique<c_driver>();
