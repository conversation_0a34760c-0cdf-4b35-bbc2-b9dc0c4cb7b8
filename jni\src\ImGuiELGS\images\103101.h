#pragma once
const unsigned char picture_103101_png[] = {

    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x4F, 0x00, 0x00, 0x00, 0x1A, 
    0x08, 0x06, 0x00, 0x00, 0x00, 0xF6, 0x77, 0x01, 0xC2, 0x00, 0x00, 0x06, 
    0x95, 0x49, 0x44, 0x41, 0x54, 0x68, 0x81, 0x62, 0x18, 0x05, 0x64, 0x02, 
    0x06, 0x06, 0x06, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x1A, 0xC5, 0xE4, 
    0x02, 0x06, 0x06, 0x06, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x62, 0x84, 
    0xE9, 0x2D, 0x2A, 0x2A, 0x1A, 0x0A, 0xE1, 0x28, 0xCE, 0xC0, 0xC0, 0xC0, 
    0x02, 0x65, 0xFF, 0x65, 0x60, 0x60, 0x78, 0xCD, 0xC0, 0xC0, 0x00, 0xF7, 
    0x03, 0x14, 0x08, 0x32, 0x30, 0x30, 0x48, 0x42, 0xD9, 0xEF, 0xA0, 0x6A, 
    0x7E, 0x33, 0x30, 0x30, 0x70, 0x21, 0xA9, 0xF9, 0xCF, 0xC0, 0xC0, 0xF0, 
    0x95, 0x1C, 0x07, 0xF4, 0xF5, 0xF5, 0x41, 0x18, 0x0C, 0x0C, 0x0C, 0x00, 
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0x62, 0x41, 0x96, 0xE8, 0xED, 0xED, 0x25, 
    0xC7, 0x3C, 0xBA, 0x80, 0xE2, 0xE2, 0xE2, 0x5C, 0x5E, 0x5E, 0xDE, 0x49, 
    0x4C, 0x4C, 0x4C, 0x60, 0xFB, 0xFE, 0xFD, 0xFB, 0xC7, 0xF0, 0xF5, 0x2B, 
    0xA6, 0xFF, 0xB9, 0xB8, 0xB8, 0x18, 0x78, 0x79, 0x79, 0xC1, 0xEC, 0x6F, 
    0xDF, 0xBE, 0x81, 0xD5, 0xFC, 0xFD, 0xFB, 0x97, 0x81, 0x8D, 0x8D, 0x0D, 
    0xAE, 0xE6, 0xFF, 0xFF, 0xFF, 0x0C, 0xBF, 0x7E, 0xFD, 0x2A, 0xEB, 0xED, 
    0xED, 0xED, 0x26, 0xC5, 0xF1, 0xC5, 0xC5, 0xC5, 0x08, 0x0E, 0x03, 0x03, 
    0x03, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x62, 0xC1, 0xA9, 0x72, 0xF0, 0x81, 
    0xCC, 0xCC, 0xCC, 0x4C, 0x06, 0x71, 0x71, 0x50, 0xE2, 0x43, 0x04, 0x1E, 
    0x23, 0x23, 0x6A, 0xC2, 0x63, 0x61, 0x61, 0x61, 0xE0, 0xE0, 0xE0, 0x00, 
    0xB3, 0x7F, 0xFF, 0xFE, 0xCD, 0xF0, 0xE7, 0xCF, 0x1F, 0x86, 0xEF, 0xDF, 
    0xBF, 0x33, 0x08, 0x09, 0x09, 0xC1, 0xD5, 0xBC, 0x7B, 0xF7, 0x8E, 0xA1, 
    0xB5, 0xB5, 0x35, 0x93, 0x81, 0x81, 0x81, 0xA4, 0xC0, 0x43, 0x01, 0x0C, 
    0x0C, 0x0C, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x62, 0x1A, 0x42, 0x81, 
    0x37, 0xF3, 0xF8, 0xF1, 0xE3, 0x70, 0x0E, 0x28, 0x05, 0x82, 0x52, 0x18, 
    0x0F, 0x0F, 0x0F, 0x0A, 0x86, 0x05, 0x1C, 0x08, 0xB0, 0xB2, 0xB2, 0x32, 
    0x70, 0x72, 0x72, 0xA2, 0x04, 0x1C, 0x08, 0x80, 0x52, 0x24, 0x03, 0x03, 
    0xC3, 0x7B, 0x8A, 0x5C, 0xC3, 0xC0, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xFF, 
    0xFF, 0x1A, 0x4A, 0x29, 0x6F, 0xD6, 0x89, 0x13, 0x27, 0x26, 0x38, 0x38, 
    0x38, 0x30, 0x08, 0x08, 0x08, 0x50, 0x64, 0x10, 0x28, 0xDB, 0x82, 0x12, 
    0x2F, 0x31, 0x6A, 0x8B, 0x8B, 0x8B, 0x41, 0x21, 0xAF, 0x0A, 0x8A, 0x0B, 
    0x06, 0x06, 0x86, 0x23, 0x70, 0x09, 0x06, 0x06, 0x06, 0x00, 0x00, 0x00, 
    0x00, 0xFF, 0xFF, 0x62, 0x41, 0x53, 0xC8, 0x09, 0xCA, 0x1E, 0x0C, 0x0C, 
    0x0C, 0xB6, 0x14, 0xB9, 0x8E, 0xBA, 0x00, 0xE4, 0x46, 0x3E, 0x10, 0xE3, 
    0xF7, 0xEF, 0xDF, 0x87, 0x5E, 0xBC, 0x78, 0x61, 0x47, 0x6A, 0xE0, 0x81, 
    0xB2, 0x2D, 0xA8, 0xDC, 0x83, 0x01, 0x68, 0xEA, 0xE4, 0x2B, 0x2E, 0x2E, 
    0xBE, 0x41, 0x40, 0x2B, 0x23, 0x17, 0x17, 0x97, 0x9A, 0x88, 0x88, 0x08, 
    0x03, 0x33, 0x33, 0x33, 0xC3, 0xFD, 0xFB, 0xF7, 0xBD, 0x18, 0x18, 0x18, 
    0xB6, 0x83, 0x65, 0x18, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 
    0x42, 0x4F, 0x79, 0x99, 0xF6, 0xF6, 0xF6, 0xBD, 0x4A, 0x4A, 0x4A, 0xF4, 
    0x0E, 0x20, 0x9C, 0x00, 0x94, 0x3D, 0x91, 0xB3, 0x22, 0x88, 0xFF, 0xE1, 
    0xC3, 0x07, 0xB0, 0x67, 0x60, 0x15, 0x03, 0x36, 0xF0, 0xEB, 0xD7, 0x2F, 
    0x70, 0xB9, 0x08, 0x02, 0x1F, 0x3F, 0x7E, 0x04, 0xF3, 0x41, 0x00, 0x54, 
    0x71, 0x80, 0xCA, 0xC5, 0xF2, 0xF2, 0x72, 0x35, 0x42, 0x76, 0x83, 0xCA, 
    0x53, 0x10, 0x06, 0x05, 0xDE, 0xD5, 0xAB, 0x57, 0x41, 0x81, 0x07, 0x4A, 
    0x58, 0x90, 0xC0, 0x63, 0x60, 0x60, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 
    0x42, 0x0F, 0xBC, 0x20, 0x5D, 0x5D, 0x5D, 0x06, 0x45, 0x45, 0x45, 0x70, 
    0x4C, 0x41, 0x93, 0x37, 0xDC, 0x20, 0x90, 0x83, 0x41, 0x80, 0x5C, 0x39, 
    0x50, 0xE1, 0x8D, 0xEE, 0x38, 0x62, 0xE4, 0xC8, 0x01, 0xC8, 0xB5, 0xAB, 
    0x84, 0x84, 0x04, 0xD9, 0xE6, 0xC0, 0xC0, 0xCF, 0x9F, 0x3F, 0x41, 0xD4, 
    0x27, 0xB8, 0x00, 0x03, 0x03, 0x03, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x42, 
    0x0F, 0x3C, 0x15, 0x50, 0x28, 0x83, 0x00, 0x3E, 0x87, 0x93, 0x2B, 0x07, 
    0x8A, 0x71, 0x72, 0xE4, 0x06, 0x03, 0x00, 0xD5, 0xDC, 0xA0, 0x12, 0x00, 
    0xEE, 0x16, 0x06, 0x06, 0x06, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x42, 
    0xAE, 0x6D, 0x19, 0x19, 0x19, 0x19, 0xC5, 0xF1, 0x65, 0x85, 0x91, 0x0C, 
    0xA0, 0x39, 0x03, 0x9C, 0xFC, 0xC0, 0x80, 0x81, 0x81, 0x01, 0x00, 0x00, 
    0x00, 0xFF, 0xFF, 0xC2, 0x68, 0xAA, 0x20, 0x67, 0xB9, 0x51, 0x80, 0x00, 
    0xD0, 0x94, 0xF7, 0x03, 0x2E, 0xC2, 0xC0, 0xC0, 0x00, 0x00, 0x00, 0x00, 
    0xFF, 0xFF, 0x42, 0x0E, 0xBC, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0xF9, 0xF8, 
    0xF1, 0x63, 0x70, 0xE3, 0x13, 0x84, 0x41, 0xB5, 0xD4, 0x28, 0x80, 0x00, 
    0x68, 0xCA, 0x43, 0x04, 0x1E, 0x03, 0x03, 0x03, 0x00, 0x00, 0x00, 0xFF, 
    0xFF, 0x42, 0x2F, 0x68, 0xA6, 0x4F, 0x9A, 0x34, 0xC9, 0x1C, 0x56, 0xBB, 
    0x81, 0x68, 0x1D, 0x1D, 0x1D, 0x8C, 0x56, 0x3C, 0x3E, 0x00, 0x52, 0x4B, 
    0x4C, 0x01, 0xAD, 0xA1, 0xA1, 0xC1, 0xC0, 0xC7, 0x07, 0x6E, 0x81, 0x90, 
    0x04, 0x40, 0x35, 0xE8, 0xBD, 0x7B, 0xF7, 0xC0, 0x34, 0xA8, 0x51, 0x2C, 
    0x25, 0x25, 0x85, 0xA2, 0xFF, 0xF3, 0xE7, 0xCF, 0x0C, 0xCF, 0x9E, 0x3D, 
    0x03, 0xB3, 0x65, 0x64, 0x64, 0x18, 0xB8, 0xB9, 0xB9, 0xA9, 0x12, 0xFD, 
    0xD0, 0xA6, 0x0E, 0xA4, 0xCA, 0x06, 0x01, 0x06, 0x06, 0x06, 0x00, 0x00, 
    0x00, 0x00, 0xFF, 0xFF, 0x42, 0xEF, 0xDB, 0x2E, 0x2C, 0x2E, 0x2E, 0x5E, 
    0xF4, 0xFD, 0xFB, 0x77, 0x7E, 0x06, 0x48, 0xFB, 0x88, 0xEF, 0xF0, 0xE1, 
    0xC3, 0x01, 0xA0, 0x3A, 0x82, 0x04, 0x7B, 0x40, 0x21, 0xAD, 0x4D, 0x48, 
    0x8D, 0xBB, 0xBB, 0x7B, 0xA2, 0x9E, 0x9E, 0x1E, 0x98, 0xF3, 0xE3, 0xC7, 
    0x0F, 0x86, 0xF7, 0xEF, 0x11, 0x0D, 0x7E, 0x50, 0xA5, 0x25, 0x2D, 0x2D, 
    0x0D, 0x6E, 0x96, 0xA0, 0x03, 0x90, 0xD8, 0xAB, 0x57, 0xAF, 0x18, 0xD6, 
    0xAE, 0x5D, 0xBB, 0x9B, 0x85, 0x85, 0xC5, 0x35, 0x37, 0x37, 0x17, 0x1C, 
    0x48, 0xC8, 0x60, 0xF3, 0xE6, 0xCD, 0x0C, 0xCF, 0x9F, 0x3F, 0xDF, 0xE9, 
    0xE1, 0xE1, 0xE1, 0xEE, 0xE8, 0xE8, 0x88, 0xB3, 0x32, 0x02, 0x15, 0x51, 
    0xCF, 0x9F, 0x3F, 0x07, 0x07, 0x0C, 0xA8, 0x27, 0x02, 0xAB, 0x2C, 0xB1, 
    0x3A, 0x18, 0x92, 0x80, 0x10, 0x0E, 0x62, 0x60, 0x60, 0x00, 0x00, 0x00, 
    0x00, 0xFF, 0xFF, 0xC2, 0x30, 0xB5, 0xB7, 0xB7, 0x17, 0x54, 0xE8, 0x7D, 
    0x80, 0x72, 0x41, 0xF4, 0x24, 0x02, 0x01, 0x41, 0x32, 0x28, 0x2E, 0x2E, 
    0x16, 0xDF, 0xB9, 0x73, 0xE7, 0xA3, 0x9D, 0x3B, 0x77, 0x06, 0x43, 0x35, 
    0x83, 0x9A, 0x00, 0x0F, 0x91, 0x0C, 0x52, 0x35, 0x34, 0x34, 0x34, 0x41, 
    0x4F, 0x55, 0xA0, 0xE6, 0x87, 0xA9, 0xA9, 0x29, 0x03, 0xA8, 0x39, 0xB5, 
    0x6F, 0xDF, 0x3E, 0xD7, 0xF7, 0xEF, 0xDF, 0x77, 0xDC, 0xBA, 0x75, 0xAB, 
    0x02, 0x14, 0x38, 0xB0, 0xD4, 0x0E, 0xAA, 0xF0, 0xA2, 0xA3, 0xA3, 0x19, 
    0x26, 0x4C, 0x98, 0xE0, 0xBE, 0x67, 0xCF, 0x1E, 0x70, 0xA0, 0xD8, 0xD8, 
    0xD8, 0x60, 0x75, 0xE3, 0xCB, 0x97, 0x2F, 0xC1, 0xA3, 0x24, 0xFF, 0xFF, 
    0xFF, 0x3F, 0xCD, 0xCE, 0xCE, 0x6E, 0x9A, 0x9F, 0x9F, 0x0F, 0xEF, 0x3B, 
    0xA3, 0x03, 0x68, 0x3B, 0x11, 0x31, 0x12, 0xC1, 0xC0, 0xC0, 0x00, 0x00, 
    0x00, 0x00, 0xFF, 0xFF, 0x1A, 0x90, 0xF6, 0x41, 0x6F, 0x6F, 0xEF, 0x4B, 
    0x06, 0x06, 0x86, 0x06, 0x28, 0xC6, 0x0A, 0x8A, 0x8B, 0x8B, 0x23, 0xCF, 
    0x9F, 0x3F, 0xAF, 0x8F, 0x26, 0xA9, 0x2E, 0x20, 0x20, 0x10, 0x00, 0x2A, 
    0x4A, 0x82, 0x83, 0x83, 0x19, 0xE6, 0xCC, 0x99, 0xA3, 0xBB, 0x75, 0xEB, 
    0xD6, 0x8E, 0xDB, 0xB7, 0x6F, 0x57, 0x24, 0x24, 0x24, 0x30, 0xB0, 0xB3, 
    0xB3, 0x83, 0x15, 0x49, 0x4A, 0x4A, 0x32, 0xF8, 0xF9, 0xF9, 0x31, 0xAC, 
    0x5B, 0xB7, 0x6E, 0xEA, 0xDE, 0xBD, 0x7B, 0xB3, 0x15, 0x14, 0x14, 0x30, 
    0x52, 0x27, 0x08, 0x80, 0x52, 0xF1, 0xFF, 0xFF, 0xFF, 0x6F, 0xF6, 0xF6, 
    0xF6, 0x9A, 0x15, 0x17, 0x17, 0xE7, 0x2C, 0x5D, 0xBA, 0x74, 0x72, 0x4A, 
    0x4A, 0x0A, 0xD6, 0xE2, 0x04, 0x23, 0xE5, 0x31, 0x30, 0x30, 0x00, 0x00, 
    0x00, 0x00, 0xFF, 0xFF, 0x1A, 0xB4, 0x8D, 0xAB, 0xDE, 0xDE, 0xDE, 0xE5, 
    0x0C, 0x0C, 0x0C, 0x20, 0x0C, 0x07, 0xC5, 0xC5, 0xC5, 0x6E, 0x6B, 0xD6, 
    0xAC, 0x09, 0x90, 0x95, 0x95, 0x65, 0x50, 0x56, 0x56, 0x66, 0x50, 0x53, 
    0x53, 0xF3, 0xBE, 0x75, 0xEB, 0xD6, 0x9A, 0x5B, 0xB7, 0x6E, 0x2D, 0xBD, 
    0x7A, 0xF5, 0x6A, 0xB4, 0x91, 0x91, 0x11, 0x5C, 0x2D, 0x28, 0xC0, 0x18, 
    0x18, 0x18, 0xAC, 0x3F, 0x7D, 0xFA, 0x34, 0xF7, 0xE9, 0xD3, 0xA7, 0xC9, 
    0xE8, 0x81, 0xF7, 0xE0, 0xC1, 0x03, 0x86, 0x55, 0xAB, 0x56, 0x81, 0x98, 
    0xD3, 0xA1, 0xF6, 0x4D, 0x29, 0x2E, 0x2E, 0x16, 0xD8, 0xB9, 0x73, 0x67, 
    0xB3, 0xBF, 0xBF, 0x3F, 0x4A, 0x23, 0x1B, 0x04, 0x40, 0x83, 0x0C, 0x0C, 
    0x0C, 0x0C, 0xA0, 0xEE, 0x2B, 0x04, 0x30, 0x30, 0x30, 0x00, 0x00, 0x00, 
    0x00, 0xFF, 0xFF, 0x1A, 0x4A, 0xA3, 0x2A, 0x20, 0x0F, 0xEE, 0xFA, 0xFC, 
    0xF9, 0x73, 0xFB, 0x99, 0x33, 0x67, 0xC0, 0x9E, 0xF3, 0xF4, 0xF4, 0x04, 
    0xA5, 0x88, 0xF9, 0xA0, 0x11, 0x97, 0x6D, 0xDB, 0xB6, 0xA1, 0xB4, 0x0E, 
    0x40, 0x23, 0x29, 0x5C, 0x5C, 0x5C, 0x06, 0x0C, 0x0C, 0x0C, 0xD2, 0xFC, 
    0xFC, 0xE0, 0x22, 0x1C, 0x05, 0x80, 0x2A, 0xC3, 0x97, 0x2F, 0x5F, 0x5E, 
    0xE9, 0xED, 0xED, 0x9D, 0x88, 0x64, 0x7E, 0xCB, 0x89, 0x13, 0x27, 0x66, 
    0x9F, 0x3B, 0x77, 0x0E, 0x43, 0x3D, 0xB4, 0xAB, 0x87, 0xE8, 0x20, 0x33, 
    0x30, 0x30, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x1A, 0x4A, 0xA3, 0x2A, 
    0x30, 0x30, 0x7D, 0xC7, 0x8E, 0x1D, 0x95, 0x9A, 0x9A, 0x9A, 0xE0, 0xAC, 
    0xE8, 0xE5, 0xE5, 0xC5, 0x70, 0xFD, 0xFA, 0xF5, 0x43, 0x20, 0x09, 0x50, 
    0x4D, 0x0B, 0x2A, 0xE3, 0x40, 0x00, 0x14, 0x38, 0xA0, 0xEC, 0xF7, 0xED, 
    0xDB, 0xB7, 0xF9, 0xA7, 0x4F, 0x9F, 0xF6, 0x78, 0xF8, 0x10, 0xB9, 0x48, 
    0x85, 0x0F, 0x4B, 0xCD, 0xC4, 0x66, 0xFE, 0x9A, 0x35, 0x6B, 0x52, 0xC5, 
    0xC4, 0xC4, 0x18, 0x90, 0xFB, 0xF8, 0x18, 0x29, 0x8F, 0x81, 0x81, 0x01, 
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0x1A, 0x72, 0x81, 0xD7, 0xDB, 0xDB, 0xFB, 
    0xB8, 0xB8, 0xB8, 0x38, 0x63, 0xF7, 0xEE, 0xDD, 0x33, 0xA2, 0xA2, 0xA2, 
    0x18, 0x9C, 0x9C, 0x9C, 0xC0, 0x18, 0x1D, 0x80, 0x52, 0x0A, 0x34, 0xB5, 
    0x5C, 0xBA, 0x70, 0xE1, 0x42, 0x38, 0xA8, 0x75, 0x84, 0xA6, 0x06, 0x34, 
    0x44, 0x0F, 0xCE, 0xB2, 0x68, 0xE6, 0x9F, 0x07, 0x95, 0xB7, 0x0B, 0x16, 
    0x2C, 0x58, 0x9E, 0x94, 0x94, 0x04, 0x8E, 0x00, 0x50, 0x2A, 0x86, 0x96, 
    0xA7, 0x3C, 0x70, 0x85, 0x0C, 0x0C, 0x0C, 0x00, 0x00, 0x00, 0x00, 0xFF, 
    0xFF, 0x1A, 0x8A, 0x29, 0x0F, 0xE4, 0xC1, 0x99, 0xC5, 0xC5, 0xC5, 0x6A, 
    0xCF, 0x9E, 0x3D, 0x2B, 0x82, 0xA6, 0x08, 0x14, 0x00, 0xCA, 0xBE, 0xA0, 
    0x94, 0xF5, 0xEB, 0xD7, 0xAF, 0x49, 0xBD, 0xBD, 0xBD, 0xA0, 0x61, 0x27, 
    0x42, 0x43, 0x4F, 0xE8, 0xE6, 0xAF, 0x28, 0x2E, 0x2E, 0x66, 0x9D, 0x3C, 
    0x79, 0x72, 0x26, 0x13, 0x13, 0x93, 0x25, 0xAC, 0x22, 0x42, 0x09, 0x6C, 
    0x06, 0x06, 0x06, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x1A, 0x6A, 0x13, 
    0x40, 0xE8, 0x40, 0x05, 0x6D, 0x62, 0x07, 0x06, 0x40, 0x8D, 0x46, 0x50, 
    0xCA, 0x22, 0x6B, 0x92, 0x07, 0x0D, 0x80, 0x12, 0x18, 0xA8, 0xC3, 0x0F, 
    0x6E, 0xC2, 0xC1, 0x27, 0x80, 0x18, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 
    0xFF, 0xFF, 0x1A, 0xC5, 0xE4, 0x02, 0x06, 0x06, 0x06, 0x00, 0x00, 0x00, 
    0x00, 0xFF, 0xFF, 0x1A, 0xC5, 0xE4, 0x02, 0x06, 0x06, 0x06, 0x00, 0x00, 
    0x00, 0x00, 0xFF, 0xFF, 0x03, 0x00, 0xCC, 0x7B, 0xF9, 0x78, 0x27, 0x79, 
    0x3C, 0x88, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 
    0x60, 0x82, 
};
