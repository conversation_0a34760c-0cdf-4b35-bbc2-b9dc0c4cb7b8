const unsigned char picture_107910_png[] = {

    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x34, 0x00, 0x00, 0x00, 0x1A, 
    0x08, 0x06, 0x00, 0x00, 0x00, 0xF8, 0xDE, 0x7F, 0xAD, 0x00, 0x00, 0x01, 
    0xC3, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0xED, 0xD6, 0x3D, 0x68, 0x14, 
    0x41, 0x18, 0xC6, 0xF1, 0xDF, 0x4A, 0x22, 0xA2, 0x11, 0x35, 0x69, 0xB4, 
    0x88, 0x06, 0x1B, 0x25, 0x2A, 0x88, 0x36, 0x11, 0x6C, 0x2D, 0x82, 0x08, 
    0xD6, 0x82, 0xA2, 0x95, 0xAD, 0x95, 0x82, 0x5A, 0xD8, 0x08, 0x42, 0x9A, 
    0xEB, 0x2C, 0x55, 0x10, 0x49, 0x17, 0x49, 0x61, 0xA1, 0x88, 0x85, 0x82, 
    0x4D, 0x82, 0x82, 0x0A, 0x16, 0x62, 0x84, 0xA0, 0x45, 0x10, 0xB5, 0x88, 
    0x11, 0x3F, 0x5F, 0x8B, 0x1B, 0xC9, 0xE6, 0x38, 0xEE, 0x23, 0x39, 0xF7, 
    0x40, 0xF7, 0x0F, 0x2F, 0x3B, 0x3B, 0x1F, 0xFB, 0xCC, 0xB3, 0xFB, 0xCE, 
    0xCC, 0x52, 0x52, 0x52, 0x52, 0xF2, 0x2F, 0x93, 0x15, 0x21, 0x12, 0x6C, 
    0xC0, 0x50, 0xAE, 0xEA, 0x65, 0xC6, 0xD7, 0xBF, 0xA1, 0x55, 0x94, 0xA1, 
    0x09, 0x1C, 0xCD, 0x55, 0xDD, 0xC3, 0x1C, 0x5E, 0xE3, 0x0B, 0xB6, 0x61, 
    0x07, 0x66, 0x31, 0x96, 0xF1, 0xAC, 0x88, 0x79, 0x2D, 0x9B, 0x60, 0x21, 
    0x88, 0x16, 0x63, 0x74, 0x25, 0x5A, 0x3D, 0x9D, 0x9A, 0x74, 0x13, 0x9E, 
    0x63, 0x75, 0x2A, 0xEF, 0xC1, 0x8F, 0x74, 0x3F, 0x87, 0x75, 0x29, 0xE0, 
    0x01, 0xDE, 0xAF, 0x44, 0x28, 0x83, 0x60, 0x00, 0x57, 0xF0, 0x13, 0x53, 
    0xB9, 0xF6, 0x0F, 0x98, 0xAF, 0x19, 0xB3, 0x15, 0xFD, 0x58, 0x68, 0x51, 
    0x63, 0x9F, 0x6A, 0x3A, 0xAD, 0xC1, 0x37, 0x8C, 0x34, 0xE8, 0x3B, 0x9D, 
    0x9E, 0xFB, 0x26, 0xE9, 0x1C, 0xCB, 0x78, 0xD7, 0xA2, 0xCE, 0x22, 0xC1, 
    0x44, 0x1B, 0x29, 0x51, 0x54, 0x4C, 0x05, 0x47, 0x82, 0x2D, 0xED, 0x78, 
    0x59, 0x95, 0xAE, 0x8F, 0xF0, 0x3D, 0xC5, 0xAF, 0xB6, 0xDF, 0x48, 0xE7, 
    0x99, 0xC7, 0x76, 0x4C, 0xE2, 0x6D, 0x32, 0x77, 0x36, 0xD8, 0xD9, 0x6C, 
    0xE0, 0x9F, 0x94, 0xDB, 0x88, 0xBD, 0xB9, 0xFA, 0x03, 0xAA, 0x3B, 0x4F, 
    0x2D, 0x33, 0x78, 0x8A, 0xF3, 0x58, 0xAB, 0x9A, 0xFB, 0xEB, 0xD1, 0x97, 
    0xCA, 0xBD, 0xCB, 0x75, 0x50, 0x87, 0xCF, 0x16, 0xD7, 0x56, 0x9E, 0x3B, 
    0xB8, 0x98, 0xF1, 0xA4, 0x83, 0x5A, 0xF5, 0x09, 0x7A, 0x83, 0x4D, 0xC1, 
    0xAE, 0xE0, 0x64, 0xF0, 0x30, 0xF8, 0xD8, 0x24, 0xB5, 0x2E, 0x07, 0x83, 
    0xC1, 0xA5, 0xE0, 0x4C, 0xF0, 0xA2, 0xC5, 0x94, 0xBC, 0x19, 0x4B, 0xCF, 
    0xB6, 0xE2, 0x08, 0xF6, 0x07, 0xA7, 0x82, 0x6B, 0xC1, 0xAB, 0x9A, 0x89, 
    0x1D, 0x4F, 0x7D, 0x46, 0x83, 0x4A, 0x70, 0x28, 0x38, 0x11, 0x3C, 0x6E, 
    0xC1, 0xD4, 0xA7, 0xE0, 0x5C, 0x57, 0x4C, 0xE5, 0x09, 0xFA, 0x92, 0xC9, 
    0x83, 0xC1, 0xED, 0xF4, 0x75, 0x6E, 0xA5, 0xF3, 0x6A, 0x28, 0xD7, 0x6F, 
    0x77, 0x6A, 0x9B, 0x0E, 0xAE, 0x37, 0x30, 0x36, 0x13, 0x0C, 0x77, 0xD1, 
    0xD2, 0x52, 0x82, 0x9E, 0x14, 0x9B, 0x83, 0xB1, 0xE0, 0x6A, 0xFE, 0x80, 
    0x4D, 0xF7, 0x83, 0xC1, 0x85, 0x06, 0xA6, 0x2A, 0x5D, 0xB4, 0xD0, 0x98, 
    0xA0, 0x3F, 0x38, 0x1D, 0xDC, 0x4D, 0x6B, 0xEC, 0x7E, 0xAE, 0xAD, 0x52, 
    0x27, 0xED, 0x6E, 0x74, 0x6D, 0x3D, 0xB5, 0x43, 0xDA, 0x64, 0x46, 0x22, 
    0xB7, 0xDB, 0x05, 0xC3, 0xC1, 0x6C, 0x30, 0x1E, 0x1C, 0x8E, 0xEA, 0x8F, 
    0x6F, 0x49, 0x49, 0x49, 0xC9, 0x7F, 0xC2, 0x6F, 0x5D, 0xC8, 0x44, 0x36, 
    0x81, 0xCB, 0xC4, 0xAB, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 
    0xAE, 0x42, 0x60, 0x82, 
};
