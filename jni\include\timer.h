#ifndef TIMER_H
#define TIMER_H
#include <time.h>
#include <string>
#include <string.h>
#include <stdio.h>
#include <unistd.h>
#include <sched.h>
#include <thread>

struct Timer {
    bool fpsio;
    int events;
    void* data;
    long long oldtimer;
    struct timespec str, end, old, now, sleep, vsync, test, strloop, endloop;
    long long lodtime;
    long long looptime;
    long long runtime;
    std::string name;
    unsigned long Fps;
    long SleepTime;
    long vsyncSleepTime;
    unsigned long old_time;
    unsigned long now_time;
    long vsynctiemr[2];

    void setname(const char * name_) {
        name = name_;
    }

    Timer(char *in_name) {
        name = in_name;
        SleepTime = 0;
        old_time = 0;
        lodtime = 0;
        now_time = 0;
        memset(&str,0, sizeof(str));
        memset(&end,0, sizeof(end));
        memset(&old,0, sizeof(old));
        memset(&now,0, sizeof(now));
    }

    Timer(unsigned int fps) {
        Fps = 1000000000 / fps;
        SleepTime = 0;
        old_time = 0;
        lodtime = 0;
        now_time = 0;
        memset(&str,0, sizeof(str));
        memset(&end,0, sizeof(end));
        memset(&old,0, sizeof(old));
        memset(&now,0, sizeof(now));
    }

    Timer() {
        SleepTime = 0;
        old_time = 0;
        lodtime = 0;
        now_time = 0;
        memset(&str,0, sizeof(str));
        memset(&end,0, sizeof(end));
        memset(&old,0, sizeof(old));
        memset(&now,0, sizeof(now));
    }

    inline void start(){
        clock_gettime(CLOCK_MONOTONIC,&str);
    }

    inline float stop(bool show){
        clock_gettime(CLOCK_MONOTONIC,&end);
        runtime = (((1000000000 * end.tv_sec) + (end.tv_nsec)) - ((1000000000 * str.tv_sec) + (str.tv_nsec)));
        return runtime / 1000000.0f;
    }

    inline void SetFps(unsigned int fps){
        Fps = 1000000000 / fps;
    }

    inline unsigned long GetFps() const {
        return Fps;
    }

    inline void FpsEnd() {
        fpsio = false;
    }

    inline void AotuFPS_init() {
        clock_gettime(CLOCK_MONOTONIC, &old);
        SleepTime = Fps;
        start();
    }

    float AotuFPS() {
        clock_gettime(CLOCK_MONOTONIC, &now);
        old_time = (((1000000000 * now.tv_sec) + (now.tv_nsec)) - ((1000000000 * old.tv_sec) + (old.tv_nsec)));
        SleepTime = Fps - old_time;
        if (SleepTime < 0) {
            SleepTime = 0;
            clock_gettime(CLOCK_MONOTONIC, &old);
            return (old_time + SleepTime) / 1000000.0f;
        }
        nsleep(SleepTime);
        clock_gettime(CLOCK_MONOTONIC, &old);
        return (old_time + SleepTime) / 1000000.0f;
    }

    inline bool cktime(unsigned int ms) {
        if (!lodtime) {
            start();
        }
        clock_gettime(CLOCK_MONOTONIC, &end);
        lodtime = (((1000000000 * end.tv_sec) + (end.tv_nsec)) - ((1000000000 * str.tv_sec) + (str.tv_nsec))) / 1000;
        if (lodtime >= ms){
            lodtime = 0;
            return true;
        } else {
            return false;
        }
    }

    bool islooptimestart;
    inline void looptimesta() {
        islooptimestart = true;
        clock_gettime(CLOCK_MONOTONIC, &strloop);
    }

    inline void looptimeend() {
        islooptimestart = false;
    }

    inline long getlooptime() {
        clock_gettime(CLOCK_MONOTONIC, &endloop);
        looptime = (((1000000000 * endloop.tv_sec) + (endloop.tv_nsec)) - ((1000000000 * strloop.tv_sec) + (strloop.tv_nsec)));
        return looptime;
    }

    int32_t getNumCpus() {
        static int32_t sNumCpus = []() {
            cpu_set_t cpuSet;
            CPU_ZERO(&cpuSet);
            sched_getaffinity(0, sizeof(cpuSet), &cpuSet); // 0 for current thread
            int32_t numCpus = CPU_COUNT(&cpuSet);
            return numCpus;
        }();
        return sNumCpus;
    }

    void SetCPUAffinity() {
        const int32_t numCpus = getNumCpus();
        cpu_set_t cpuSet;
        CPU_ZERO(&cpuSet);
        for (int32_t cpu = 0; cpu < numCpus; ++cpu) {
            CPU_SET(cpu, &cpuSet);
        }
        sched_setaffinity(0, sizeof(cpuSet), &cpuSet); // 0 for current thread
    }

    void nsleep(long delay) {
        struct timespec req, rem;
        long nano_delay = delay;
        while(nano_delay > 0) {
            rem.tv_sec = 0;
            rem.tv_nsec = 0;
            req.tv_sec = 0;
            req.tv_nsec = nano_delay;
            nanosleep(&req, &rem);
            nano_delay = rem.tv_nsec;
        };
        return ;
    }
};

#endif
