LOCAL_PATH := $(call my-dir)

include $(CLEAR_VARS)
# 0 Vulkan 1 OpenGL
OPENGL_DRAW = 0

LOCAL_MODULE := sb内部.sh

LOCAL_CFLAGS := -w -std=c++2a
LOCAL_CFLAGS += -fvisibility=hidden
LOCAL_CPPFLAGS := -w -std=c++2a
LOCAL_CPPFLAGS += -fvisibility=hidden
LOCAL_CPPFLAGS += -O3LOCAL_CFLAGS
LOCAL_CPPFLAGS += -O3

LOCAL_CFLAGS += -DVK_USE_PLATFORM_ANDROID_KHR
LOCAL_CPPFLAGS += -DVK_USE_PLATFORM_ANDROID_KHR

ifeq ($(OPENGL_DRAW), 1)
   LOCAL_CFLAGS += -DUSE_OPENGL
   LOCAL_CPPFLAGS += -DUSE_OPENGL
endif

LOCAL_C_INCLUDES += $(LOCAL_PATH)/include
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/Android_check
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/Android_draw
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/Android_read
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/Android_touch
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/Android_vulkan
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/ImGui
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/ImGui/backends

LOCAL_SRC_FILES := \
    src/main.cpp \
    src/Android_draw/draw.cpp \
    src/Android_touch/TouchHelperA.cpp \
    src/ImGui/imgui.cpp \
    src/ImGui/imgui_demo.cpp \
    src/ImGui/imgui_draw.cpp \
    src/ImGui/imgui_tables.cpp \
    src/ImGui/imgui_widgets.cpp \
    src/ImGui/backends/imgui_impl_android.cpp \

ifeq ($(OPENGL_DRAW), 1)
    LOCAL_SRC_FILES += src/ImGui/backends/imgui_impl_opengl3.cpp
else
    LOCAL_SRC_FILES += src/ImGui/backends/imgui_impl_vulkan.cpp 
    LOCAL_SRC_FILES += src/Android_vulkan/vulkan_wrapper.cpp 
    LOCAL_SRC_FILES += src/Android_vulkan/VulkanUtils.cpp
endif

LOCAL_LDLIBS := -llog -landroid -lEGL -lGLESv2 -lGLESv3

include $(BUILD_EXECUTABLE)