#pragma once
#include <fstream>
#include <fcntl.h>
#include <iostream>
#include <unistd.h>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/sysmacros.h>
#include <cstring>
#include <cstdio>
#include "kma_driver.h"
using namespace std;
typedef unsigned short UTF16;
#if defined(__aarch64__) && defined(__ANDROID__)
#include "Log.h"
#include "VecTool.h"
#endif

struct Transform
{
    VecTor3 Scale3D;
    VecTor4 Rotation;
    VecTor3 Translation;
};

struct FMatrix
{
    float M[4][4];
};

class ImGuiTOOL
{
public:
    pid_t Pid = -1;
    Driver *driver = nullptr;
    float Matrix[4][4] = {0};
    UTF16 BUFFER16[16] = {0};
    Transform MeshTrans, MeshTran;
    uintptr_t ModulesBase[10] = {0};

    TOUCH_INFORMATION touch_information;
    FMatrix XMatrix, BoneMatrix, OutcMatrix;
    uintptr_t MeshAddress = 0, BoneAddress = 0;
    RESOLUTION_INFORMATION resolution_information;
    IMGUISWITCH_INFORMATION imguiswitch_information;

    // 初始化驱动
    void initialize()
    {
        if (driver == nullptr)
        {
            driver = new Driver();
        }
        if (Pid != -1)
        {
            driver->initpid(Pid);
        }
    }

    // 使用 driver.h 的 read 方法，不使用 read_safe
    bool read(uintptr_t address, void *buffer, size_t size)
    {
        if (driver == nullptr)
        {
            return false;
        }
        return driver->read(address, buffer, size);
    }

    bool write(uintptr_t address, void *buffer, size_t size)
    {
        if (driver == nullptr)
        {
            return false;
        }
        return driver->write(address, buffer, size);
    }

    int readcount(int *c, int num)
    {
        ++*c;
        return num;
    }

    template <typename start>
    start read(uintptr_t address)
    {
        start buffer;
        if (read(address, &buffer, sizeof(start)))
        {
            return buffer;
        }
        return {};
    }

    template <typename start>
    bool read(uintptr_t address, start *buffer)
    {
        return read(address, buffer, sizeof(start));
    }

    template <typename... s>
    uintptr_t GetPointer(uintptr_t address, s... args)
    {
        int count = 0;
        uintptr_t last_address = 0;
        int array[] = {(readcount(&count, args))...};
        read(address + array[0], &last_address);
        for (int i = 1; i < count; i++)
        {
            if (i == count - 1)
            {
                last_address += array[i];
                return last_address;
            }
            read(last_address + array[i], &last_address);
        }
        return last_address;
    }

    void GetPid(const char *name)
    {
        if (driver == nullptr)
        {
            return;
        }
        Pid = driver->get_pid((char *)name);
        if (Pid > 0)
        {
            driver->initpid(Pid);
        }
    }

    uintptr_t GetModuleAddressOne(const char *name)
    {
        char *pch;
        char line[1024] = {0};
        uintptr_t address = 0;
        char filename[64] = {0};
        snprintf(filename, sizeof(filename), "/proc/%d/maps", Pid);
        FILE *file = fopen(filename, "r");
        if (file != nullptr)
        {
            while (fgets(line, sizeof(line), file))
            {
                if (strstr(line, name))
                {
                    pch = strtok(line, "-");
                    address = strtoul(pch, NULL, 16);
                    if (address == 0x8000)
                    {
                        address = 0;
                    }
                    break;
                }
            }
            fclose(file);
        }
        return address;
    }

    uintptr_t GetModuleAddressTwo(char *name)
    {
        if (driver == nullptr)
        {
            return 0;
        }
        return driver->get_module_base(Pid, name);
    }

    string exec(const char *str)
    {
        string result = "";
        char buffer[1024] = "";
        FILE *PIPE = popen(str, "r");
        if (!PIPE)
        {
            return "";
        }
        while (!feof(PIPE))
        {
            if (fgets(buffer, sizeof(buffer), PIPE) != nullptr)
            {
                result += buffer;
            }
        }
        pclose(PIPE);
        return result;
    }

    ImGuiTOOL()
    {
        // 初始化 Driver 对象
        driver = new Driver();

        memset(&touch_information, 0, sizeof(TOUCH_INFORMATION));
        memset(&resolution_information, 0, sizeof(RESOLUTION_INFORMATION));
        memset(&imguiswitch_information, 0, sizeof(IMGUISWITCH_INFORMATION));
    }

    ~ImGuiTOOL()
    {
        if (driver != nullptr)
        {
            delete driver;
            driver = nullptr;
        }
    }
};
