const unsigned char picture_602075_png[] = {

    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x1A, 
    0x08, 0x06, 0x00, 0x00, 0x00, 0xA9, 0x4A, 0x4C, 0xCE, 0x00, 0x00, 0x00, 
    0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0B, 0x13, 0x00, 0x00, 0x0B, 
    0x13, 0x01, 0x00, 0x9A, 0x9C, 0x18, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52, 
    0x47, 0x42, 0x00, 0xAE, 0xCE, 0x1C, 0xE9, 0x00, 0x00, 0x00, 0x04, 0x67, 
    0x41, 0x4D, 0x41, 0x00, 0x00, 0xB1, 0x8F, 0x0B, 0xFC, 0x61, 0x05, 0x00, 
    0x00, 0x01, 0x95, 0x49, 0x44, 0x41, 0x54, 0x78, 0x01, 0xB5, 0x95, 0xE1, 
    0x6D, 0x83, 0x40, 0x0C, 0x85, 0xAD, 0x4E, 0xC0, 0x08, 0x8C, 0x90, 0x11, 
    0xE8, 0x04, 0xA1, 0x13, 0x84, 0x0D, 0xC2, 0x06, 0xB9, 0x0D, 0x9A, 0x0D, 
    0xD2, 0x4E, 0x40, 0x37, 0xA0, 0x5D, 0xA0, 0xD9, 0xA0, 0x74, 0x82, 0xD2, 
    0x09, 0xEA, 0xDA, 0xCA, 0x3B, 0x61, 0x39, 0xF0, 0xE3, 0x8E, 0xE4, 0x49, 
    0x4F, 0x4A, 0x38, 0x72, 0x9F, 0xFD, 0x7C, 0x01, 0x22, 0x27, 0x66, 0xAE, 
    0xC4, 0x25, 0xDD, 0x53, 0x02, 0x38, 0xF0, 0xA4, 0xEE, 0x2E, 0x50, 0x07, 
    0x19, 0xC4, 0xBD, 0x78, 0x14, 0x07, 0x71, 0x4D, 0xB7, 0x90, 0x83, 0x8C, 
    0x00, 0xA9, 0x6B, 0x74, 0x76, 0xD6, 0x7B, 0x68, 0x8D, 0x64, 0x83, 0xC6, 
    0x41, 0x22, 0x68, 0x27, 0x7E, 0xC1, 0xE7, 0x11, 0xF7, 0xED, 0x29, 0x53, 
    0x0F, 0xE2, 0x12, 0x9F, 0xBF, 0xC5, 0xBF, 0xF0, 0xAB, 0xF8, 0x49, 0xBC, 
    0xC5, 0x7A, 0xA1, 0x35, 0x89, 0x3F, 0xB2, 0x61, 0xF2, 0xC3, 0x02, 0x15, 
    0xC7, 0xEA, 0x5B, 0xF1, 0x9B, 0xE9, 0x6E, 0x34, 0x1D, 0x6B, 0x97, 0x9B, 
    0x35, 0xB0, 0x67, 0x37, 0x93, 0x08, 0x28, 0xB1, 0xB1, 0x87, 0xE9, 0x01, 
    0x69, 0x29, 0x55, 0x00, 0x34, 0x38, 0x69, 0x83, 0xDD, 0x14, 0xEB, 0x76, 
    0x8E, 0x71, 0x96, 0x27, 0xCA, 0x11, 0x5F, 0xFE, 0x33, 0xEC, 0xAA, 0x57, 
    0x55, 0x58, 0x6F, 0xCD, 0x35, 0x8D, 0x79, 0xAF, 0xDD, 0x52, 0x8E, 0xB0, 
    0xC1, 0x16, 0xD1, 0xD8, 0xEA, 0x37, 0x88, 0x31, 0x42, 0x76, 0x98, 0x63, 
    0xB3, 0x06, 0x76, 0xC0, 0xA6, 0x61, 0x26, 0xAE, 0x08, 0xE9, 0x79, 0x9A, 
    0x63, 0x83, 0xEE, 0x76, 0x94, 0xAA, 0x05, 0x58, 0x84, 0xA8, 0x6A, 0xCC, 
    0x52, 0x21, 0x27, 0x5C, 0x3B, 0x72, 0xCE, 0x69, 0x04, 0xAC, 0x71, 0x90, 
    0x13, 0xBA, 0x61, 0x07, 0x61, 0xC4, 0x9B, 0x77, 0xF4, 0x51, 0x65, 0x40, 
    0x07, 0x1A, 0x53, 0x37, 0xD3, 0x09, 0xBB, 0x59, 0xAA, 0x03, 0xE5, 0x08, 
    0x31, 0xB6, 0xAE, 0x93, 0x71, 0x26, 0xDE, 0x08, 0xAB, 0x71, 0x8F, 0x3E, 
    0x0C, 0x92, 0x58, 0x11, 0x76, 0x15, 0x17, 0xD6, 0x3C, 0x6C, 0x34, 0xD0, 
    0x92, 0x52, 0xC5, 0x97, 0x27, 0xC8, 0x55, 0x5C, 0x0B, 0xB0, 0x2F, 0x5C, 
    0xDB, 0x52, 0xAA, 0x00, 0x0A, 0x0B, 0x71, 0x15, 0xF8, 0x3E, 0x60, 0x96, 
    0x8F, 0x58, 0x6B, 0x93, 0xE3, 0x03, 0xEC, 0x38, 0x33, 0x9B, 0xC1, 0xC4, 
    0xD5, 0xF3, 0x74, 0xFC, 0xE3, 0xC3, 0xB9, 0xA0, 0x54, 0xA1, 0xF2, 0xCE, 
    0xCC, 0xCC, 0xC2, 0x2C, 0x24, 0x9E, 0x50, 0x55, 0xA0, 0x1C, 0x01, 0x16, 
    0x37, 0xB5, 0x71, 0xD5, 0x06, 0x38, 0x98, 0x22, 0x2A, 0xCA, 0x15, 0x3A, 
    0x1A, 0xE0, 0x33, 0xBA, 0x9B, 0x83, 0x04, 0x5A, 0x2B, 0x74, 0xB6, 0xC7, 
    0xDC, 0x7E, 0xC4, 0xEF, 0x06, 0xF2, 0x77, 0x13, 0x88, 0x83, 0x55, 0xF0, 
    0x27, 0x00, 0xEA, 0xF4, 0x17, 0x63, 0x22, 0x78, 0xF6, 0xD5, 0xF1, 0x0F, 
    0xB8, 0xC0, 0x36, 0x7E, 0x7C, 0xA8, 0x6C, 0xAD, 0x00, 0x00, 0x00, 0x00, 
    0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82, 
};
