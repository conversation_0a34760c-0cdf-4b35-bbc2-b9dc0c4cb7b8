#ifndef SHA256_H
#define SHA256_H

#include <cstring>
#include <string>
#include <fstream>
#include <sstream>
#include <vector>
#include <iomanip>
#include <unistd.h>

class SHA256 {
private:
    typedef unsigned char BYTE;
    typedef unsigned int WORD;

    const WORD K[64] = {
        0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5,
        0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
        0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3,
        0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
        0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc,
        0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
        0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7,
        0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
        0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13,
        0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
        0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3,
        0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
        0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5,
        0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
        0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208,
        0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
    };

    WORD state[8] = {
        0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a,
        0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19
    };

    inline WORD ROTR(WORD x, int n) {
        return (x >> n) | (x << (32 - n));
    }

    inline WORD CH(WORD x, WORD y, WORD z) {
        return (x & y) ^ (~x & z);
    }

    inline WORD MAJ(WORD x, WORD y, WORD z) {
        return (x & y) ^ (x & z) ^ (y & z);
    }

    inline WORD EP0(WORD x) {
        return ROTR(x, 2) ^ ROTR(x, 13) ^ ROTR(x, 22);
    }

    inline WORD EP1(WORD x) {
        return ROTR(x, 6) ^ ROTR(x, 11) ^ ROTR(x, 25);
    }

    inline WORD SIG0(WORD x) {
        return ROTR(x, 7) ^ ROTR(x, 18) ^ (x >> 3);
    }

    inline WORD SIG1(WORD x) {
        return ROTR(x, 17) ^ ROTR(x, 19) ^ (x >> 10);
    }

    void transform(const BYTE* data) {
        WORD W[64];
        WORD a, b, c, d, e, f, g, h;
        WORD T1, T2;

        // 准备消息调度
        for (int i = 0; i < 16; ++i) {
            W[i] = (data[i * 4] << 24) | (data[i * 4 + 1] << 16) |
                   (data[i * 4 + 2] << 8) | (data[i * 4 + 3]);
        }

        for (int i = 16; i < 64; ++i) {
            W[i] = SIG1(W[i - 2]) + W[i - 7] + SIG0(W[i - 15]) + W[i - 16];
        }

        // 初始化工作变量
        a = state[0];
        b = state[1];
        c = state[2];
        d = state[3];
        e = state[4];
        f = state[5];
        g = state[6];
        h = state[7];

        // 主循环
        for (int i = 0; i < 64; ++i) {
            T1 = h + EP1(e) + CH(e, f, g) + K[i] + W[i];
            T2 = EP0(a) + MAJ(a, b, c);
            h = g;
            g = f;
            f = e;
            e = d + T1;
            d = c;
            c = b;
            b = a;
            a = T1 + T2;
        }

        // 更新状态
        state[0] += a;
        state[1] += b;
        state[2] += c;
        state[3] += d;
        state[4] += e;
        state[5] += f;
        state[6] += g;
        state[7] += h;
    }

public:
    static std::string GetProcExePath() {
        char path[256];
        ssize_t len = readlink("/proc/self/exe", path, sizeof(path)-1);
        if (len != -1) {
            path[len] = '\0';
            return std::string(path);
        }
        return "";
    }

    std::string CalculateFileHash(const std::string& filepath) {
        std::ifstream file(filepath, std::ios::binary);
        if (!file) {
            return "";
        }

        // 重置状态
        state[0] = 0x6a09e667;
        state[1] = 0xbb67ae85;
        state[2] = 0x3c6ef372;
        state[3] = 0xa54ff53a;
        state[4] = 0x510e527f;
        state[5] = 0x9b05688c;
        state[6] = 0x1f83d9ab;
        state[7] = 0x5be0cd19;

        BYTE buffer[64];
        size_t totalBytes = 0;

        // 处理完整的数据块
        while (file.read(reinterpret_cast<char*>(buffer), 64)) {
            transform(buffer);
            totalBytes += 64;
        }

        // 处理最后的数据块
        size_t remainingBytes = file.gcount();
        totalBytes += remainingBytes;

        // 添加填充
        memset(buffer + remainingBytes, 0, 64 - remainingBytes);
        buffer[remainingBytes] = 0x80;

        if (remainingBytes >= 56) {
            transform(buffer);
            memset(buffer, 0, 64);
        }

        // 添加消息长度（以位为单位）
        uint64_t bitLen = totalBytes * 8;
        buffer[63] = bitLen & 0xFF;
        buffer[62] = (bitLen >> 8) & 0xFF;
        buffer[61] = (bitLen >> 16) & 0xFF;
        buffer[60] = (bitLen >> 24) & 0xFF;
        buffer[59] = (bitLen >> 32) & 0xFF;
        buffer[58] = (bitLen >> 40) & 0xFF;
        buffer[57] = (bitLen >> 48) & 0xFF;
        buffer[56] = (bitLen >> 56) & 0xFF;
        transform(buffer);

        // 生成最终哈希值
        std::stringstream ss;
        for (int i = 0; i < 8; i++) {
            ss << std::hex << std::setw(8) << std::setfill('0') << state[i];
        }
        return ss.str();
    }
};

#endif // SHA256_H 