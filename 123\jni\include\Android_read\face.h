#include <algorithm>
#include <arpa/inet.h>
#include <cctype>
#include <chrono>
#include <codecvt>
#include <cstdlib>
#include <ctime>
#include <deque>
#include <dirent.h>
#include <dlfcn.h>
#include <errno.h>
#include <fcntl.h>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <locale>
#include <malloc.h>
#include <map>
#include <math.h>
#include <netdb.h>
#include <netinet/in.h>
#include <pthread.h>
#include <regex.h>
#include <sstream>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <string.h>
#include <sys/fcntl.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/syscall.h>
#include <sys/sysmacros.h>
#include <sys/types.h>
#include <sys/uio.h>
#include <sys/user.h>
#include <sys/wait.h>
#include <thread>
#include <unistd.h>
#include <vector>

using namespace std;

void 防人脸() {
cout << "\n1.开启防人脸 2.关闭防人脸" << endl;
cout << "防上号脸 设备脸 举报脸" << endl;
cout << "说明:\n在QQ微信登陆界面开启防人脸 游戏途中请勿关闭\n如果重进游戏将无法登陆游戏 此时请划掉游戏关闭防人脸\n关闭防人脸后即可正常登陆游戏\n请勿在上游戏前开启防人脸 否则无法登陆游戏\n请勿在登陆大厅后开启防人脸 否则封号\n千万不要在登陆大厅后执行开启防人脸 登陆界面开启\n千万不要在到达登陆界面前开启防人脸 登陆界面开启\n千万不要在打开游戏进程后执行关闭防人脸 打开游戏前关闭" << endl;
cout << "请输入选项" << endl;
int chooseface;
cin >> chooseface;
if (chooseface == 1) {
cout << "等待提示开启成功后再退出\n";

system("iptables -A OUTPUT -p tcp --dport 8013 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 8085 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 8081 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 5692 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 20297 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 8019 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 1884 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 15692 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 10112 -j DROP");
system("iptables -A OUTPUT -p tcp -d api.xunyou.mobi --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d api.unipay.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d szmg.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d h.trace.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d portal.xunyou.mobi --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d cloud.tgpa.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d cloudctrl.gcloud.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d android.crashsight.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d *********** --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d snowflake.qq.com --dport 443 -j DROP");

system("iptables -A OUTPUT -p tcp --dport 8013 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 8085 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 8081 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 5692 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 20297 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 8019 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 1884 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 15692 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 10112 -j DROP");
system("iptables -A OUTPUT -p tcp -d api.xunyou.mobi --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d api.unipay.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d szmg.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d h.trace.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d portal.xunyou.mobi --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d cloud.tgpa.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d cloudctrl.gcloud.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d android.crashsight.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d *********** --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d snowflake.qq.com --dport 443 -j DROP");

system("iptables -A OUTPUT -p tcp --dport 8013 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 8085 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 8081 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 5692 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 20297 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 8019 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 1884 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 15692 -j DROP");
system("iptables -A OUTPUT -p tcp --dport 10112 -j DROP");
system("iptables -A OUTPUT -p tcp -d api.xunyou.mobi --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d api.unipay.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d szmg.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d h.trace.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d portal.xunyou.mobi --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d cloud.tgpa.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d cloudctrl.gcloud.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d android.crashsight.qq.com --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d *********** --dport 443 -j DROP");
system("iptables -A OUTPUT -p tcp -d snowflake.qq.com --dport 443 -j DROP");

cout << "防人脸开启成功\n";
} else if (chooseface == 2) {
cout << "等待提示关闭成功后再退出\n";

system("rm -rf /sdcard/ramdump");
system("rm -rf /data/user_de/0/com.tencent.mf.uam/code_cache/*");
system("rm -rf /data/user/0/com.tencent.mf.uam/cache/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/cache/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/tbslog/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/tencent/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/UE4Game/UAGame/UAGame/Saved/Logs/*");
system("dmesg -c >/dev/null 2>&1");
system("logcat -c -b main -b events -b radio -b system >/dev/null 2>&1");
system("iptables -F");
system("iptables -X");
system("iptables -Z");
system("iptables -t nat -F");
system("rm -rf /data/data/com.tencent.mf.uam/files/ano_tmp");
system("rm -rf /data/user/0/com.tencent.mf.uam/files/ano_tmp");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/SaveGames");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/Config/Android/Updater.ini");
system("rm -rf /data/data/com.tencent.mf.uam/files/estv-tkboxtv/");
system("rm -rf /data/data/com.tencent.mf.uam/files/ShadowDir/Unpacked/estv-dynamic-manager/");
system("rm -rf /data/data/com.tencent.mf.uam/files/com.gcloudsdk.gcloud.gvoice/tqos/");
system("rm -rf /data/data/com.tencent.mf.uam/files/com.gcloudsdk.gcloud.gvoice/GVoiceLog/");
system("echo 16384 > /proc/sys/fs/inotify/max_queued_events");
system("echo 128 > /proc/sys/fs/inotify/max_user_instances");
system("echo 8192 > /proc/sys/fs/inotify/max_user_watches");

system("rm -rf /sdcard/ramdump");
system("rm -rf /data/user_de/0/com.tencent.mf.uam/code_cache/*");
system("rm -rf /data/user/0/com.tencent.mf.uam/cache/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/cache/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/tbslog/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/tencent/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/UE4Game/UAGame/UAGame/Saved/Logs/*");
system("dmesg -c >/dev/null 2>&1");
system("logcat -c -b main -b events -b radio -b system >/dev/null 2>&1");
system("iptables -F");
system("iptables -X");
system("iptables -Z");
system("iptables -t nat -F");
system("rm -rf /data/data/com.tencent.mf.uam/files/ano_tmp");
system("rm -rf /data/user/0/com.tencent.mf.uam/files/ano_tmp");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/SaveGames");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/Config/Android/Updater.ini");
system("rm -rf /data/data/com.tencent.mf.uam/files/estv-tkboxtv/");
system("rm -rf /data/data/com.tencent.mf.uam/files/ShadowDir/Unpacked/estv-dynamic-manager/");
system("rm -rf /data/data/com.tencent.mf.uam/files/com.gcloudsdk.gcloud.gvoice/tqos/");
system("rm -rf /data/data/com.tencent.mf.uam/files/com.gcloudsdk.gcloud.gvoice/GVoiceLog/");
system("echo 16384 > /proc/sys/fs/inotify/max_queued_events");
system("echo 128 > /proc/sys/fs/inotify/max_user_instances");
system("echo 8192 > /proc/sys/fs/inotify/max_user_watches");

system("rm -rf /sdcard/ramdump");
system("rm -rf /data/user_de/0/com.tencent.mf.uam/code_cache/*");
system("rm -rf /data/user/0/com.tencent.mf.uam/cache/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/cache/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/tbslog/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/tencent/*");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/UE4Game/UAGame/UAGame/Saved/Logs/*");
system("dmesg -c >/dev/null 2>&1");
system("logcat -c -b main -b events -b radio -b system >/dev/null 2>&1");
system("iptables -F");
system("iptables -X");
system("iptables -Z");
system("iptables -t nat -F");
system("rm -rf /data/data/com.tencent.mf.uam/files/ano_tmp");
system("rm -rf /data/user/0/com.tencent.mf.uam/files/ano_tmp");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/SaveGames");
system("rm -rf /storage/emulated/0/Android/data/com.tencent.mf.uam/files/UE4Game/ShadowTrackerExtra/ShadowTrackerExtra/Saved/Config/Android/Updater.ini");
system("rm -rf /data/data/com.tencent.mf.uam/files/estv-tkboxtv/");
system("rm -rf /data/data/com.tencent.mf.uam/files/ShadowDir/Unpacked/estv-dynamic-manager/");
system("rm -rf /data/data/com.tencent.mf.uam/files/com.gcloudsdk.gcloud.gvoice/tqos/");
system("rm -rf /data/data/com.tencent.mf.uam/files/com.gcloudsdk.gcloud.gvoice/GVoiceLog/");
system("echo 16384 > /proc/sys/fs/inotify/max_queued_events");
system("echo 128 > /proc/sys/fs/inotify/max_user_instances");
system("echo 8192 > /proc/sys/fs/inotify/max_user_watches");

cout << "防人脸关闭成功\n";
} else {
cout << "选项有误\n" << endl;
}
}