#include "driver.h"

#define PI 3.1416

int 屏幕X = 0, 屏幕Y = 0;
typedef unsigned short UTF16;
typedef char UTF8;

float 矩阵数组[16] = {0};
int 骨骼列表[][2]{
	{15, 82}, // 锁骨 脖子
	{15, 1},  // 锁骨 盆骨
	{15, 53}, // 锁骨 左肩膀
	{53, 54}, // 左肩膀 左手肘
	{54, 87}, // 左手肘 左手踝
	{15, 23}, // 锁骨 右肩膀
	{23, 24}, // 右肩膀 右手肘
	{24, 86}, // 右手肘 右手踝
	{1, 2},	  // 屁眼 左屁股
	{2, 4},	  // 左屁股 左膝盖
	{4, 92},  // 左膝盖 左脚跟
	{1, 7},	  // 屁眼 右屁股
	{7, 9},	  // 右屁股 右膝盖
	{9, 94},  // 右膝盖 右脚跟
};
void ImGuiColor()
{
	ImGuiStyle &Style = ImGui::GetStyle();
	Style.GrabRounding = 5;
	Style.GrabMinSize = 20;
	Style.WindowRounding = 5.0f;   // 设置边框圆角
	Style.FrameBorderSize = 3.0f;  // 设置控件描边宽度
	Style.WindowBorderSize = 5.0f; // 设置框架描边宽度
	Style.FrameRounding = 10.0f;   // 控件圆角
	ImVec4 *colors = Style.Colors;
	colors[ImGuiCol_Text] = ImVec4(1.00f, 1.00f, 1.00f, 1.00f);
	colors[ImGuiCol_TextDisabled] = ImVec4(0.50f, 0.20f, 0.20f, 1.00f);
	colors[ImGuiCol_WindowBg] = ImVec4(0.95f, 0.90f, 0.95f, 1.00f);
	colors[ImGuiCol_ChildBg] = ImVec4(0.90f, 0.90f, 0.90f, 1.00f);
	colors[ImGuiCol_PopupBg] = ImVec4(0.95f, 0.95f, 0.95f, 1.00f);
	colors[ImGuiCol_FrameBg] = ImVec4(0.85f, 0.85f, 0.85f, 1.00f);
	colors[ImGuiCol_FrameBgHovered] = ImVec4(1.00f, 0.79f, 0.93f, 0.40f);
	colors[ImGuiCol_FrameBgActive] = ImVec4(1.00f, 0.79f, 0.93f, 0.67f);
	colors[ImGuiCol_TitleBg] = ImVec4(0.96f, 0.96f, 0.96f, 1.00f);
	colors[ImGuiCol_TitleBgActive] = ImVec4(1.00f, 0.79f, 0.93f, 1.00f);
	colors[ImGuiCol_TitleBgCollapsed] = ImVec4(1.00f, 0.98f, 0.98f, 0.50f);
	colors[ImGuiCol_MenuBarBg] = ImVec4(0.86f, 0.86f, 0.86f, 1.00f);
	colors[ImGuiCol_ScrollbarBg] = ImVec4(0.98f, 0.98f, 0.98f, 0.53f);
	colors[ImGuiCol_ScrollbarGrab] = ImVec4(1.00f, 0.78f, 0.93f, 0.31f);
	colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(1.00f, 0.78f, 0.93f, 1.00f);
	colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(1.00f, 0.78f, 0.93f, 0.89f);
	colors[ImGuiCol_CheckMark] = ImVec4(1.00f, 0.78f, 0.93f, 1.00f);
	colors[ImGuiCol_SliderGrab] = ImVec4(1.00f, 0.78f, 0.93f, 0.31f);
	colors[ImGuiCol_SliderGrabActive] = ImVec4(1.00f, 0.78f, 0.93f, 1.00f);
	colors[ImGuiCol_Button] = ImVec4(1.00f, 0.78f, 0.93f, 0.31f);
	colors[ImGuiCol_ButtonHovered] = ImVec4(1.00f, 0.78f, 0.93f, 1.00f);
	colors[ImGuiCol_ButtonActive] = ImVec4(1.00f, 0.78f, 0.93f, 0.89f);
	colors[ImGuiCol_Header] = ImVec4(1.00f, 0.78f, 0.93f, 0.31f);
	colors[ImGuiCol_HeaderHovered] = ImVec4(1.00f, 0.78f, 0.93f, 0.80f);
	colors[ImGuiCol_HeaderActive] = ImVec4(1.00f, 0.78f, 0.93f, 1.00f);
	colors[ImGuiCol_SeparatorHovered] = ImVec4(1.00f, 0.78f, 0.93f, 0.78f);
	colors[ImGuiCol_SeparatorActive] = ImVec4(1.00f, 0.78f, 0.93f, 1.00f);
	colors[ImGuiCol_ResizeGrip] = ImVec4(1.00f, 0.78f, 0.93f, 0.20f);
	colors[ImGuiCol_ResizeGripHovered] = ImVec4(1.00f, 0.78f, 0.93f, 0.60f);
	colors[ImGuiCol_ResizeGripActive] = ImVec4(1.00f, 0.78f, 0.93f, 0.90f);
	colors[ImGuiCol_Tab] = ImVec4(0.86f, 0.86f, 0.86f, 1.00f);
	colors[ImGuiCol_TabHovered] = ImVec4(1.00f, 0.78f, 0.93f, 0.40f);
	colors[ImGuiCol_TabActive] = ImVec4(1.00f, 0.78f, 0.93f, 0.60f);
	colors[ImGuiCol_TabUnfocused] = ImVec4(0.95f, 0.95f, 0.95f, 0.80f);
	colors[ImGuiCol_TabUnfocusedActive] = ImVec4(1.00f, 0.78f, 0.93f, 0.40f);
	colors[ImGuiCol_PlotHistogram] = ImVec4(0.85f, 0.85f, 0.85f, 1.00f);
	colors[ImGuiCol_PlotHistogramHovered] = ImVec4(1.00f, 0.78f, 0.93f, 1.00f);
	colors[ImGuiCol_TextSelectedBg] = ImVec4(1.00f, 0.78f, 0.93f, 0.43f);
	colors[ImGuiCol_NavHighlight] = ImVec4(1.00f, 0.78f, 0.93f, 1.00f);
	Style.WindowBorderSize = 1.00f;
	Style.ChildBorderSize = 1.00f;
	Style.PopupBorderSize = 1.00f;
	Style.FrameBorderSize = 1.00f;
}
void 读取字符(UTF8 *buf, uintptr_t namepy)
{
	UTF16 buf16[16] = {0};
	driver->read(namepy, buf16, 28);
	UTF16 *pTempUTF16 = buf16;
	UTF8 *pTempUTF8 = buf;
	UTF8 *pUTF8End = pTempUTF8 + 32;
	while (pTempUTF16 < pTempUTF16 + 28)
	{
		if (*pTempUTF16 <= 0x007F && pTempUTF8 + 1 < pUTF8End)
		{
			*pTempUTF8++ = (UTF8)*pTempUTF16;
		}
		else if (*pTempUTF16 >= 0x0080 && *pTempUTF16 <= 0x07FF && pTempUTF8 + 2 < pUTF8End)
		{
			*pTempUTF8++ = (*pTempUTF16 >> 6) | 0xC0;
			*pTempUTF8++ = (*pTempUTF16 & 0x3F) | 0x80;
		}
		else if (*pTempUTF16 >= 0x0800 && *pTempUTF16 <= 0xFFFF && pTempUTF8 + 3 < pUTF8End)
		{
			*pTempUTF8++ = (*pTempUTF16 >> 12) | 0xE0;
			*pTempUTF8++ = ((*pTempUTF16 >> 6) & 0x3F) | 0x80;
			*pTempUTF8++ = (*pTempUTF16 & 0x3F) | 0x80;
		}
		else
		{
			break;
		}
		pTempUTF16++;
	}
}

struct Vector2A
{
	float X;
	float Y;

	Vector2A()
	{
		this->X = 0;
		this->Y = 0;
	}

	Vector2A(float x, float y)
	{
		this->X = x;
		this->Y = y;
	}

	bool operator<(const Vector2A &other) const
	{
		return X < other.X || (X == other.X && Y < other.Y);
	}
};

struct D2DVector
{
	float X;
	float Y;
};

struct Vector3A
{
	float X;
	float Y;
	float Z;

	inline Vector3A() : X(0), Y(0), Z(0) {}

	inline Vector3A(float X, float Y, float Z) : X(X), Y(Y), Z(Z) {}

	// inline  eXplicit Vector3A(float value) : X(value), Y(value), Z(value) {}

	inline Vector3A operator+(const Vector3A &other) const
	{
		return Vector3A(X + other.X, Y + other.Y, Z + other.Z);
	}

	inline Vector3A operator+(const float other) const
	{
		return Vector3A(X + other, Y + other, Z + other);
	}

	inline Vector3A operator-(const Vector3A &other) const
	{
		return Vector3A(X - other.X, Y - other.Y, Z - other.Z);
	}

	inline Vector3A operator-(const float other) const
	{
		return Vector3A(X - other, Y - other, Z - other);
	}

	inline Vector3A operator*(const Vector3A &other) const
	{
		return Vector3A(X * other.X, Y * other.Y, Z * other.Z);
	}

	inline Vector3A operator*(const float value) const
	{
		return Vector3A(X * value, Y * value, Z * value);
	}

	inline Vector3A operator/(const float value) const
	{
		if (value != 0)
		{
			return Vector3A(X / value, Y / value, Z / value);
		}
		return Vector3A();
	}

	inline Vector3A operator-() const
	{
		return Vector3A(-X, -Y, -Z);
	}

	inline Vector3A &operator+=(const Vector3A &other)
	{
		X += other.X;
		Y += other.Y;
		Z += other.Z;
		return *this;
	}

	inline Vector3A &operator-=(const Vector3A &other)
	{
		X -= other.X;
		Y -= other.Y;
		Z -= other.Z;
		return *this;
	}

	inline Vector3A &operator+=(const float value)
	{
		X += value;
		Y += value;
		Z += value;
		return *this;
	}

	inline Vector3A &operator-=(const float value)
	{
		X -= value;
		Y -= value;
		Z -= value;
		return *this;
	}

	inline Vector3A &operator*=(const float value)
	{
		X *= value;
		Y *= value;
		Z *= value;
		return *this;
	}

	inline Vector3A &operator*=(const Vector3A &other)
	{
		X *= other.X;
		Y *= other.Y;
		Z *= other.Z;
		return *this;
	}

	inline Vector3A &operator/=(const float &value)
	{
		X /= value;
		Y /= value;
		Z /= value;
		return *this;
	}

	inline Vector3A &operator=(const Vector3A &other)
	{
		X = other.X;
		Y = other.Y;
		Z = other.Z;
		return *this;
	}

	inline bool operator==(const Vector3A &other) const
	{
		return X == other.X && Y == other.Y && Z == other.Z;
	}

	inline bool operator!=(const Vector3A &other) const
	{
		return X != other.X || Y != other.Y || Z != other.Z;
	}

	inline float operator[](int indeX) const
	{
		return (&X)[indeX];
	}

	inline float &operator[](int indeX)
	{
		return (&X)[indeX];
	}

	inline void Zero()
	{
		X = Y = Z = 0;
	}

	inline float length() const
	{
		return sqrt(X * X + Y * Y + Z * Z);
	}

	inline bool isValid() const
	{
		return X != 0 && Y != 0 && Z != 0 && !isnan(X) && !isnan(Y) && !isnan(Z);
	}

	static inline float dot(const Vector3A &a, const Vector3A &b)
	{
		return a.X * b.X + a.Y * b.Y + a.Z * b.Z;
	}

	static inline bool inRange(const Vector3A &target, const Vector3A &min, const Vector3A &max)
	{
		return target.X > min.X && target.X < max.X && target.Y > min.Y && target.Y < max.Y &&
			   target.Z > min.Z && target.Z < max.Z;
	}
};

struct FMatrix
{
	float M[4][4];
};

class FRotator
{
public:
	FRotator() : Pitch(0.f), Yaw(0.f), Roll(0.f)
	{
	}
	FRotator(float _Pitch, float _Yaw, float _Roll) : Pitch(_Pitch), Yaw(_Yaw), Roll(_Roll)
	{
	}
	~FRotator()
	{
	}
	float Pitch;
	float Yaw;
	float Roll;
	inline FRotator Clamp()
	{

		if (Pitch > 180)
		{
			Pitch -= 360;
		}
		else
		{
			if (Pitch < -180)
			{
				Pitch += 360;
			}
		}
		if (Yaw > 180)
		{
			Yaw -= 360;
		}
		else
		{
			if (Yaw < -180)
			{
				Yaw += 360;
			}
		}
		if (Pitch > 89)
		{
			Pitch = 89;
		}
		if (Pitch < -89)
		{
			Pitch = -89;
		}
		while (Yaw < 180)
		{
			Yaw += 360;
		}
		while (Yaw > 180)
		{
			Yaw -= 360;
		}
		Roll = 0;
		return FRotator(Pitch, Yaw, Roll);
	}
	inline float Length()
	{
		return sqrtf(Pitch * Pitch + Yaw * Yaw + Roll * Roll);
	}
	FRotator operator+(FRotator v)
	{
		return FRotator(Pitch + v.Pitch, Yaw + v.Yaw, Roll + v.Roll);
	}
	FRotator operator-(FRotator v)
	{
		return FRotator(Pitch - v.Pitch, Yaw - v.Yaw, Roll - v.Roll);
	}
};

struct Quat
{
	float X;
	float Y;
	float Z;
	float W;
};

struct FTransform
{
	Quat Rotation;
	Vector3A Translation;
	Vector3A Scale3D;
};

float get_3D_Distance(float Self_x, float Self_y, float Self_z, float Object_x, float Object_y, float Object_z)
{
	float x, y, z;
	x = Self_x - Object_x;
	y = Self_y - Object_y;
	z = Self_z - Object_z;
	return (float)(sqrt(x * x + y * y + z * z));
}

Vector2A rotateCoord(float angle, float objRadar_x, float objRadar_y)
{
	Vector2A radarCoordinate;
	float s = sin(angle * PI / 180);
	float c = cos(angle * PI / 180);
	radarCoordinate.X = objRadar_x * c + objRadar_y * s;
	radarCoordinate.Y = -objRadar_x * s + objRadar_y * c;
	return radarCoordinate;
}

Vector2A WorldToScreen(Vector3A obj, float matrix[16], float ViewW)
{
	float x = ((float)屏幕X / 2) +
			  (matrix[0] * obj.X + matrix[4] * obj.Y + matrix[8] * obj.Z + matrix[12]) / ViewW * ((float)屏幕X / 2);
	float y = ((float)屏幕Y / 2) -
			  (matrix[1] * obj.X + matrix[5] * obj.Y + matrix[9] * obj.Z + matrix[13]) / ViewW * ((float)屏幕Y / 2);

	return Vector2A(x, y);
}

Vector3A MatrixToVector(FMatrix matrix)
{
	return Vector3A(matrix.M[3][0], matrix.M[3][1], matrix.M[3][2]);
}

FMatrix MatrixMulti(FMatrix m1, FMatrix m2)
{
	FMatrix matrix = FMatrix();
	for (int i = 0; i < 4; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			for (int k = 0; k < 4; k++)
			{
				matrix.M[i][j] += m1.M[i][k] * m2.M[k][j];
			}
		}
	}
	return matrix;
}

FMatrix TransformToMatrix(FTransform transform)
{
	FMatrix matrix;
	matrix.M[3][0] = transform.Translation.X;
	matrix.M[3][1] = transform.Translation.Y;
	matrix.M[3][2] = transform.Translation.Z;
	float x2 = transform.Rotation.X + transform.Rotation.X;
	float y2 = transform.Rotation.Y + transform.Rotation.Y;
	float z2 = transform.Rotation.Z + transform.Rotation.Z;
	float xx2 = transform.Rotation.X * x2;
	float yy2 = transform.Rotation.Y * y2;
	float zz2 = transform.Rotation.Z * z2;
	matrix.M[0][0] = (1 - (yy2 + zz2)) * transform.Scale3D.X;
	matrix.M[1][1] = (1 - (xx2 + zz2)) * transform.Scale3D.Y;
	matrix.M[2][2] = (1 - (xx2 + yy2)) * transform.Scale3D.Z;
	float yz2 = transform.Rotation.Y * z2;
	float wx2 = transform.Rotation.W * x2;
	matrix.M[2][1] = (yz2 - wx2) * transform.Scale3D.Z;
	matrix.M[1][2] = (yz2 + wx2) * transform.Scale3D.Y;
	float xy2 = transform.Rotation.X * y2;
	float wz2 = transform.Rotation.W * z2;
	matrix.M[1][0] = (xy2 - wz2) * transform.Scale3D.Y;
	matrix.M[0][1] = (xy2 + wz2) * transform.Scale3D.X;
	float xz2 = transform.Rotation.X * z2;
	float wy2 = transform.Rotation.W * y2;
	matrix.M[2][0] = (xz2 + wy2) * transform.Scale3D.Z;
	matrix.M[0][2] = (xz2 - wy2) * transform.Scale3D.X;
	matrix.M[0][3] = 0;
	matrix.M[1][3] = 0;
	matrix.M[2][3] = 0;
	matrix.M[3][3] = 1;
	return matrix;
}

typedef uint64_t ptr64;
Vector3A GetVector3Pos(ptr64 MBone, ptr64 Mhuman)
{
	Vector3A results;
	FTransform Mheadtrans = driver->read<FTransform>(MBone + 82 * 48);
	FTransform Mmeshtrans = driver->read<FTransform>(Mhuman);
	FMatrix Mc2wMatrix = TransformToMatrix(Mmeshtrans);
	FMatrix MboneMatrix = TransformToMatrix(Mheadtrans);
	Vector3A RR = MarixToVector(MatrixMulti(MboneMatrix, Mc2wMatrix));
	return results = RR;
}

FTransform getBone(uintptr_t addr)
{
	FTransform transform;
	driver->read(addr, &transform, 4 * 10);
	return transform;
}

struct D3DXMATRIX
{
	float _11;
	float _12;
	float _13;
	float _14;
	float _21;
	float _22;
	float _23;
	float _24;
	float _31;
	float _32;
	float _33;
	float _34;
	float _41;
	float _42;
	float _43;
	float _44;
};

struct D3DXVECTOR4
{
	float X;
	float Y;
	float Z;
	float W;
};

struct FTransform1
{
	D3DXVECTOR4 Rotation;
	Vector3A Translation;
	Vector3A Scale3D;
};

D3DXMATRIX ToMatrixWithScale(D3DXVECTOR4 Rotation, Vector3A Translation, Vector3A Scale3D)
{
	D3DXMATRIX M;
	float X2, Y2, Z2, xX2, Yy2, Zz2, Zy2, Wx2, Xy2, Wz2, Zx2, Wy2;
	M._41 = Translation.X;
	M._42 = Translation.Y;
	M._43 = Translation.Z;
	X2 = Rotation.X + Rotation.X;
	Y2 = Rotation.Y + Rotation.Y;
	Z2 = Rotation.Z + Rotation.Z;
	xX2 = Rotation.X * X2;
	Yy2 = Rotation.Y * Y2;
	Zz2 = Rotation.Z * Z2;
	M._11 = (1 - (Yy2 + Zz2)) * Scale3D.X;
	M._22 = (1 - (xX2 + Zz2)) * Scale3D.Y;
	M._33 = (1 - (xX2 + Yy2)) * Scale3D.Z;
	Zy2 = Rotation.Y * Z2;
	Wx2 = Rotation.W * X2;
	M._32 = (Zy2 - Wx2) * Scale3D.Z;
	M._23 = (Zy2 + Wx2) * Scale3D.Y;
	Xy2 = Rotation.X * Y2;
	Wz2 = Rotation.W * Z2;
	M._21 = (Xy2 - Wz2) * Scale3D.Y;
	M._12 = (Xy2 + Wz2) * Scale3D.X;
	Zx2 = Rotation.X * Z2;
	Wy2 = Rotation.W * Y2;
	M._31 = (Zx2 + Wy2) * Scale3D.Z;
	M._13 = (Zx2 - Wy2) * Scale3D.X;
	M._14 = 0;
	M._24 = 0;
	M._34 = 0;
	M._44 = 1;
	return M;
}

FTransform1 ReadFTransform(uintptr_t address)
{
	FTransform1 Result;
	Result.Rotation.X = driver->读取浮点数(address);
	Result.Rotation.Y = driver->读取浮点数(address + 4);
	Result.Rotation.Z = driver->读取浮点数(address + 8);
	Result.Rotation.W = driver->读取浮点数(address + 12);
	Result.Translation.X = driver->读取浮点数(address + 16);
	Result.Translation.Y = driver->读取浮点数(address + 20);
	Result.Translation.Z = driver->读取浮点数(address + 24);
	Result.Scale3D.X = driver->读取浮点数(address + 32);
	Result.Scale3D.Y = driver->读取浮点数(address + 36);
	Result.Scale3D.Z = driver->读取浮点数(address + 40);
	return Result;
}

Vector3A D3dMatrixMultiply(D3DXMATRIX bonematrix, D3DXMATRIX actormatrix)
{
	Vector3A result;
	result.X = bonematrix._41 * actormatrix._11 + bonematrix._42 * actormatrix._21 + bonematrix._43 * actormatrix._31 +
			   bonematrix._44 * actormatrix._41;
	result.Y = bonematrix._41 * actormatrix._12 + bonematrix._42 * actormatrix._22 + bonematrix._43 * actormatrix._32 +
			   bonematrix._44 * actormatrix._42;
	result.Z = bonematrix._41 * actormatrix._13 + bonematrix._42 * actormatrix._23 + bonematrix._43 * actormatrix._33 +
			   bonematrix._44 * actormatrix._43;
	return result;
}

Vector3A getBoneXYZ(uintptr_t humanAddr, uintptr_t boneAddr, int Part)
{
	FTransform1 Bone = ReadFTransform(boneAddr + Part * 48);
	FTransform1 Actor = ReadFTransform(humanAddr);
	D3DXMATRIX Bone_Matrix = ToMatrixWithScale(Bone.Rotation, Bone.Translation, Bone.Scale3D);
	D3DXMATRIX Component_ToWorld_Matrix = ToMatrixWithScale(Actor.Rotation, Actor.Translation, Actor.Scale3D);
	Vector3A result = D3dMatrixMultiply(Bone_Matrix, Component_ToWorld_Matrix);
	return result;
}

double ArcToAngle(double angle)
{
	return angle * (double)57.29577951308;
}

Vector2A getPointingAngles(Vector3A Self, Vector3A xyz)
{
	Vector2A PointingAngle;
	float zbcx = xyz.X - Self.X;
	float zbcy = xyz.Y - Self.Y;
	float zbcz = xyz.Z - Self.Z;
	float pfg = (float)sqrt((zbcx * zbcx) + (zbcy * zbcy));
	PointingAngle.X = (float)atan2(zbcy, zbcx) * 180 / PI;
	PointingAngle.Y = (float)atan2(zbcz, pfg) * 180 / PI;
	return PointingAngle;
}

Vector2A CalcAngle(Vector3A D, Vector3A W)
{
	float x = W.X - D.X;
	float y = W.Y - D.Y;
	float z = W.Z - D.Z;
	Vector2A PointingAngle;
	PointingAngle.X = atan2(y, x) * 180 / PI;
	PointingAngle.Y = atan2(z, sqrt(x * x + y * y)) * 180 / PI;
	return PointingAngle;
}

struct MinimalViewInfo
{
	Vector3A Location;
	FRotator Rotation;
	float FOV;
};

FMatrix RotToMatrix(FRotator rotation)
{
	float radPitch = rotation.Pitch * ((float)M_PI / 180.0f);
	float radYaw = rotation.Yaw * ((float)M_PI / 180.0f);
	float radRoll = rotation.Roll * ((float)M_PI / 180.0f);

	float SP = sinf(radPitch);
	float CP = cosf(radPitch);
	float SY = sinf(radYaw);
	float CY = cosf(radYaw);
	float SR = sinf(radRoll);
	float CR = cosf(radRoll);

	FMatrix matrix;

	matrix.M[0][0] = (CP * CY);
	matrix.M[0][1] = (CP * SY);
	matrix.M[0][2] = (SP);
	matrix.M[0][3] = 0;

	matrix.M[1][0] = (SR * SP * CY - CR * SY);
	matrix.M[1][1] = (SR * SP * SY + CR * CY);
	matrix.M[1][2] = (-SR * CP);
	matrix.M[1][3] = 0;

	matrix.M[2][0] = (-(CR * SP * CY + SR * SY));
	matrix.M[2][1] = (CY * SR - CR * SP * SY);
	matrix.M[2][2] = (CR * CP);
	matrix.M[2][3] = 0;

	matrix.M[3][0] = 0;
	matrix.M[3][1] = 0;
	matrix.M[3][2] = 0;
	matrix.M[3][3] = 1;

	return matrix;
}

Vector2A WorldToScreen2(Vector3A worldLocation, MinimalViewInfo camViewInfo)
{
	FMatrix tempMatrix = RotToMatrix(camViewInfo.Rotation);

	Vector3A vAxisX(tempMatrix.M[0][0], tempMatrix.M[0][1], tempMatrix.M[0][2]);
	Vector3A vAxisY(tempMatrix.M[1][0], tempMatrix.M[1][1], tempMatrix.M[1][2]);
	Vector3A vAxisZ(tempMatrix.M[2][0], tempMatrix.M[2][1], tempMatrix.M[2][2]);

	Vector3A vDelta = worldLocation - camViewInfo.Location;

	Vector3A vTransformed(Vector3A::dot(vDelta, vAxisY), Vector3A::dot(vDelta, vAxisZ), Vector3A::dot(vDelta, vAxisX));

	if (vTransformed.Z < 1.0f)
	{
		vTransformed.Z = 1.0f;
	}

	float fov = camViewInfo.FOV;
	float screenCenterX = displayInfo.width / 2;
	float screenCenterY = displayInfo.height / 2;

	return Vector2A(
		(screenCenterX + vTransformed.X * (screenCenterX / tanf(fov * ((float)M_PI / 360.0f))) / vTransformed.Z),
		(screenCenterY - vTransformed.Y * (screenCenterX / tanf(fov * ((float)M_PI / 360.0f))) / vTransformed.Z));
}

Vector3A MarixToVector(FMatrix matrix)
{
	return Vector3A(matrix.M[3][0], matrix.M[3][1], matrix.M[3][2]);
}

Vector2A 骨骼转屏幕点(Vector3A 对象坐标)
{
	Vector2A 返回点;
	float camera = 矩阵数组[3] * 对象坐标.X + 矩阵数组[7] * 对象坐标.Y + 矩阵数组[11] * 对象坐标.Z + 矩阵数组[15];
	返回点.X = displayInfo.width / 2 + (矩阵数组[0] * 对象坐标.X + 矩阵数组[4] * 对象坐标.Y + 矩阵数组[8] * 对象坐标.Z + 矩阵数组[12]) / camera * displayInfo.width / 2;
	返回点.Y = displayInfo.height / 2 - (矩阵数组[1] * 对象坐标.X + 矩阵数组[5] * 对象坐标.Y + 矩阵数组[9] * 对象坐标.Z + 矩阵数组[13]) / camera * displayInfo.height / 2;

	return 返回点;
}

Vector3A 获取骨骼坐标(uint64_t 骨骼矩阵, uint64_t 骨骼对象, int 骨骼索引)
{

	FTransform meshtrans = getBone(骨骼矩阵);
	FMatrix c2wMatrix = TransformToMatrix(meshtrans);

	FTransform headtrans = getBone(骨骼对象 + 骨骼索引 * 48);
	FMatrix boneMatrix = TransformToMatrix(headtrans);
	Vector3A relLocation = MarixToVector(MatrixMulti(boneMatrix, c2wMatrix));
	return relLocation;
}

Vector3A GetBoneLocation(uint64_t BoneMatix, uint64_t BoneAddr, int BoneIndex)
{

	FTransform meshtrans = getBone(BoneMatix);
	FMatrix c2wMatrix = TransformToMatrix(meshtrans);

	FTransform headtrans = getBone(BoneAddr + BoneIndex * 48);
	FMatrix boneMatrix = TransformToMatrix(headtrans);
	Vector3A relLocation = MatrixToVector(MatrixMulti(boneMatrix, c2wMatrix));
	return relLocation;
}

string 获取类名(uintptr_t GName, int ID)
{
	char 类名[128];
	uintptr_t 缓冲偏移 = ID >> 16;
	uintptr_t 类名偏移 = ID & 65535;
	uintptr_t 类名缓冲 = driver->读取指针(GName + 0x30 + 0x10 + (缓冲偏移 * 0x8));
	uintptr_t 类名指针 = 类名缓冲 + (0x2 * 类名偏移);
	uintptr_t 类名定位 = 类名指针 + 0x2;
	driver->read(类名定位, &类名, 128);
	return 类名;
}

void 绘制字体描边(float size, int x, int y, ImVec4 color, const char *str)
{
	ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x + 1, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
	ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x - 0.1, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
	ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y + 1), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
	ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y - 1), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
	ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y), ImGui::ColorConvertFloat4ToU32(color), str);
}

ImColor 白色 = ImColor(255, 255, 255);
ImColor 红色 = ImColor(255, 0, 0);
ImColor 黄色 = ImColor(255, 255, 0);
ImColor 黑色 = ImColor(0, 0, 0);
ImColor 绿色 = ImColor(0, 255, 0);
ImColor 蓝色 = ImColor(0, 0, 255);
ImColor 青色 = ImColor(137, 207, 240);

enum
{
	ECharacterType_None = 0,
	ECharacterType_PMC = 1,
	ECharacterType_SCAV = 2,
	ECharacterType_AI_SCAV = 3,
	ECharacterType_AI_SCAV_BOSS = 4,
	ECharacterType_AI_PMC = 5,
	ECharacterType_AI_ELIT = 6,
	ECharacterType_BOSS = 7,
	ECharacterType_AI_SCAV_Follower = 8,
	ECharacterType_MAX = 9
};

uintptr_t libbase, Uworld, Tersafe, Master, GName, PlayerWorld, Matrix, Arrayaddr, Uleve, Count, GameState, PlayerCount, PlayerArray, MySelf, Objaddr, IsBot, Mesh, human, Bone, Location, Myteam, PlayerCounts, BotCounts, TeamID, GameInstance, LocalPlayers, Player, PlayerController, SGCharacterWeaponManagerComponent, CurrentWeapon, SGWeaponImpact, DisplayName, WeaponAssembleComp, CachedMagazine, ChangeWeaponOwner, ContainDataList, CharacterInventoryManagerComponent, CharacterArmorManagerComponent, ProtectiveArmorList, InventoryArray, InventoryGet, SGInventoryCommonData, InventoryTypeComponent, SGCharacterWeaponManagerComponentSelf, CurrentWeaponSelf, SGWeaponImpactSelf, DisplayNameSelf, WeaponAssembleCompSelf, CachedMagazineSelf, ChangeWeaponOwnerSelf, ContainDataListSelf;
char extras[16], extra[16], PlayerName[64], MyName[64], 手持[64], 手持Self[64];
ImU32 NowCol;
Vector2A Head;
Vector2A Chest;
Vector2A Pelvis;
Vector2A Left_Shoulder;
Vector2A Right_Shoulder;
Vector2A Left_Elbow;
Vector2A Right_Elbow;
Vector2A Left_Wrist;
Vector2A Right_Wrist;
Vector2A Left_Thigh;
Vector2A Right_Thigh;
Vector2A Left_Knee;
Vector2A Right_Knee;
Vector2A Left_Ankle;
Vector2A Right_Ankle;
int 热成像判断 = 0, 死亡状态 = 0, 弹量, 最大弹量, 头, 甲, 最大循环次数, 库存ID, StandardPrice, TotalCount, 弹量Self, 最大弹量Self, NameID;
float matrix[16] = {0}, BaseEyeHeight, angle, camera, r_x, r_y, r_w, Distance, 骨骼X偏差, 骨骼Y偏差, X, Y, W, MIDDLE, BOTTOM, TOP, 价值, 负重, 价值约数, MaxEffectTime;
short bNetLoadOnClient;
uint8_t DeadCharacterType;
string 负重字符串, 价值字符串, 物资名字 = "¥", 背敌, 手持SelfStr, 弹量SelfStr, 最大弹量SelfStr, Self, GNames, PlayerInfo, BotInfo;
Vector3A 自身坐标, 敌人坐标;

bool 修复偏框 = false;
bool 绘制世界;
bool 绘制类名;
bool 绘制盒子 = true;
bool 绘制尸体 = true;

bool 绘制射线;
// bool 绘制方框 = true;
bool 绘制骨骼 = true;

bool 绘制信息;
bool 显热成像 = true;
bool 绘制人数 = true;

bool 绘制队友;
bool 绘制人机 = true;
bool 绘制准心 = true;

int 坐标切换 = 0;
int 矩阵切换;
bool 数组切换;
std::map<long, Vector3A> PosMap;
Vector3A Pos;
Vector3A ProblemPos;