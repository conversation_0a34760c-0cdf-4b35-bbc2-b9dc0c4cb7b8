#include "imgui.h"
#include "ImGuiStructure.h"

float tm1 = 127 / 255.f;
ImVec4 arr[] = {{144 / 255.f, 238 / 255.f, 144 / 255.f, tm1}, {135 / 255.f, 206 / 255.f, 255 / 255.f, tm1}, {255 / 255.f, 0 / 255.f, 0 / 255.f, tm1}, {0 / 255.f, 255 / 255.f, 0 / 255.f, tm1}, {0 / 255.f, 255 / 255.f, 127 / 255.f, tm1}, {255 / 255.f, 182 / 255.f, 193 / 255.f, tm1}, {218 / 255.f, 112 / 255.f, 214 / 255.f, tm1}, {248 / 255.f, 248 / 255.f, 255 / 255.f, tm1}, {0 / 255.f, 255 / 255.f, 255 / 255.f, tm1}, {255 / 255.f, 165 / 255.f, 0 / 255.f, tm1}, {153 / 255.f, 204 / 255.f, 255 / 255.f, tm1}, {204 / 255.f, 255 / 255.f, 153 / 255.f, tm1}, {255 / 255.f, 255 / 255.f, 153 / 255.f, tm1}, {255 / 255.f, 153 / 255.f, 153 / 255.f, tm1}, {153 / 255.f, 153 / 255.f, 204 / 255.f, tm1}, {204 / 255.f, 204 / 255.f, 204 / 255.f, tm1}, {102 / 255.f, 204 / 255.f, 153 / 255.f, tm1}, {255 / 255.f, 102 / 255.f, 0 / 255.f, tm1}, {102 / 255.f, 204 / 255.f, 204 / 255.f, tm1}, {153 / 255.f, 204 / 255.f, 255 / 255.f, tm1}};
int length = sizeof(arr) / 16;

ImColor RandomColor()
{
    int R = 0;
    int G = 0;
    int B = 0;
    int A = 180;
    R = (random() % 255);
    G = (random() % 255);
    B = (random() % 255);
    return ImColor(R, G, B, A);
}

ImColor ArrayColor[100];
void InitializeColor()
{
    for (int Count = 0; Count < 100; Count++)
    {
        ArrayColor[Count] = RandomColor();
    }
}

ImColor TeamColor(int Count)
{
    if (Count < 100 && Count > 0)
    {
        return ArrayColor[Count];
    }
    else
    {
        return ImColor(100, 255, 100, 180);
    }
}

// 定义枚举类型来表示不同的血条类型
enum HealthBarType
{
    CircleArc,
    RectangleFilled,
    CustomRectangle
};

void skeleton(ImDrawList *drawlist, const ImVec2 &start, const ImVec2 &end, ImColor color, float thickness)
{
    drawlist->AddLine(start, end, color, thickness);
} // 骨骼

// 获取基于血量的颜色
ImColor GetHealthColor(float health, bool useAlpha = true)
{
    // 基于血量返回相应颜色
    int alpha = useAlpha ? 127 : 210;

    if (health >= 80.0f)
        return ImColor(10, 240, 10, alpha);
    else if (health >= 60.0f)
        return ImColor(255, 255, 0, alpha);
    else if (health >= 30.0f)
        return ImColor(255, 0, 0, alpha);
    else
        return ImColor(127, 0, 0, alpha);
}

// 绘制血条
void DrawHealthBar(ImDrawList *drawlist, SHARED_INFORMATION *shared_information, HealthBarType healthBarType, const IMGUISWITCH_INFORMATION &imguiswitch_information, int objectCount)
{
    // 获取当前对象的屏幕坐标和血量
    const float health = shared_information[objectCount].Health;
    const float screenX = shared_information[objectCount].ScreenCoordinates.x;
    const float screenY = shared_information[objectCount].HeadSkeletonCoordinates;
    
    switch (healthBarType)
    {
    case CircleArc:
        {
            // 计算血量角度并绘制圆弧
            drawlist->AddCircleArc(
                {screenX, screenY - 60.0f},       // 圆心位置
                30.0f,                            // 半径
                {0, health * 3.6f},               // 0到健康百分比对应的角度
                GetHealthColor(health, false),    // 根据血量获取颜色
                0,                                // 自动计算分段数
                5.0f);                            // 圆弧厚度
        }
        break;
        
    case RectangleFilled:
        {
            // 绘制矩形血条
            const float barWidth = 150.0f;
            const ImVec2 barPos = {screenX - 75.0f, screenY - 30.0f};
            
            // 填充血条
            drawlist->AddRectFilled(
                barPos,
                {barPos.x + 1.5f * health, barPos.y + 20.0f},
                TeamColor(shared_information[objectCount].Team),
                30.0f);
            
            // 绘制边框
            drawlist->AddRect(
                barPos,
                {barPos.x + barWidth, barPos.y + 20.0f},
                ImColor(0, 0, 0, 200),
                30.0f,
                0,
                1.5f);
        }
        break;
        
    case CustomRectangle:
        {
            // 绘制自定义矩形血条（无需条件检查）
            const float barWidth = 120.0f;
            const float barHeight = 8.0f;
            const ImVec2 barPos = {
                screenX - 60.0f, 
                screenY - 30.0f + 6.0f + 15.0f 
            };
            
            // 填充血条
            drawlist->AddRectFilled(
                barPos,
                {barPos.x + 1.2f * health, barPos.y + barHeight},
                ImColor(255, 192, 203));
            
            // 绘制边框
            drawlist->AddRect(
                barPos,
                {barPos.x + barWidth, barPos.y + barHeight},
                ImColor(0, 0, 0, 200));
            
            // 绘制分段线
            const float segmentWidth = barWidth / 5.0f;
            for (int i = 0; i < 5; ++i) {
                float xPosition = barPos.x + (i * segmentWidth);
                drawlist->AddLine(
                    {xPosition, barPos.y},
                    {xPosition, barPos.y + barHeight},
                    ImColor(0, 0, 0, 255));
            }
        }
        break;
    }
}
