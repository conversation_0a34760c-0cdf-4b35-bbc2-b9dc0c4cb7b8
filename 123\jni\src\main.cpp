#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <fcntl.h>
#include <dirent.h>
#include <pthread.h>
#include <fstream>
#include <string.h>
#include <time.h>
#include <malloc.h>
#include <iostream>
#include <fstream>
#include "res/weiyan.h"
#include "res/cJSON.h"
#include "res/cJSON.c"
#include "res/Encrypt.h"
#include<iostream>
#include<ctime>
using namespace std;
#include "main.h"
#include "DrawGame.h"
#include "snake.h"
//#include “check.h“
#include "face.h"
bool main_thread_flag = true;
char *appgg;
int checkversion = 0;
bool containsSP = false;
    
    ImGuiStyle& style = ImGui::GetStyle();
    
    static bool 驱动挂载 = false;
const static char *name = " 绝恋@JNXZ666";

int main(int argc, char *argv[])
{

            const static char *_wyHost = "wy.llua.cn";
	const static char *_wyAppid = "68867";	
	const static char *_wyAppkey = "Fgb4UpapghS0OTo";
	const static char *_wyRc4key = "2YNUtZt9GA9pLqx";	
	const static char *_kmPath = "/sdcard/Tkm";
	const static char *_imeiPath = "/sdcard/Timei";
	const static bool _ggSwitch = false;
	std::string bbh = "1.0.0";

	if (_ggSwitch){
	    char _ggUrl[1024];
	    sprintf(_ggUrl, "app=%s",_wyAppid);
    	char *_ggData = httppost(_wyHost,"api/?id=notice",_ggUrl);
    	char* _deggData=Decrypt(_ggData, _wyRc4key);
    	cJSON *_ggJson = cJSON_Parse(_deggData);
    	int _ggCode = cJSON_GetObjectItem(_ggJson, "code")->valueint;
    	if (_ggCode == 200){
    		cJSON *_ggMsg = cJSON_GetObjectItem(_ggJson, "msg");
            char *_appgg = cJSON_GetObjectItem(_ggMsg, "app_gg")->valuestring;
    	    printf("\n\n公告:%s\n\n",_appgg);
    	}
    	
    	char _gxUrl[1024];
	    sprintf(_gxUrl, "&app=%s",_wyAppid);
    	char *gx = httppost(_wyHost,"api/?id=ini",_gxUrl);
	    char* _gxData=Decrypt(gx, _wyRc4key);
    	cJSON *_gxJson = cJSON_Parse(_gxData);
    	int _gxCode = cJSON_GetObjectItem(_gxJson, "code")->valueint;
    	//printf("%s----%s-----%s",_gxJson,_gxUrl,_gxData);
    	if (_gxCode == 200) {
    	    cJSON *_gxMsg = cJSON_GetObjectItem(_gxJson, "msg");
    	    char *version = cJSON_GetObjectItem(_gxMsg, "version")->valuestring;
    	    char *app_update_show = cJSON_GetObjectItem(_gxMsg, "app_update_show")->valuestring;
    	    if (string(bbh) == version) {
    	        	// 粉红色
			    printf("无需更新\n");
			} else {
			    	// 粉红色
		    	printf("%s\n",app_update_show);
		    	exit(0);
		    }
    	}
	}
	
	home_main:
	char _Kami[40];
	if (fopen(_kmPath, "r") == NULL)
	{		
		printf("请输入卡密:");
        char _inputKm[] = "";
	    scanf("%s",&_inputKm);
        FILE *fp = fopen(_kmPath, "w");
        if (fp != NULL) {
            fprintf(fp, "%s", _inputKm);
		    fclose(fp);
        }
        std::cout << "写入成功！正在重新验证卡密" << std::endl;
	}
	fscanf(fopen(_kmPath, "r"), "%s", &_Kami);
	char _Imei[40];
	if (fopen(_imeiPath, "r") == NULL)
	{		
		printf("设备码获取失败\n");
		srand(time(NULL));
        char* _Str = (char*)malloc((20 + 1) * sizeof(char));
        for (int i = 0; i < 20; i++) {
            int _randomNum = rand() % 26;
            _Str[i] = 'a' + _randomNum;
        }
        _Str[20] = '\0';
    
        FILE *fp = fopen(_imeiPath, "w");
        if (fp == NULL) {
            printf("文件创建失败");
            return 0;
        }
        fprintf(fp, "%s", _Str);
        fclose(fp);
        std::cout << "设备码已重新获取！正在重新验证卡密" << std::endl;
	}
	fscanf(fopen(_imeiPath, "r"), "%s", &_Imei);
//	printf("卡密： %s\n设备码： %s\n\n", _Kami, _Imei);
	if (_Kami == "" or _Imei == "")
	{
		
		printf("无设备码或者卡密");
		return 0;
	}
	time_t _Timet = time(NULL);
	int _Time = time(&_Timet);
    srand(time(NULL));
	char _Value[1024];
	char _Sign[1024];
	char _Data[1024];
	sprintf(_Value, "%d%d", _Time,rand());
	sprintf(_Sign, "kami=%s&markcode=%s&t=%d&%s", _Kami, _Imei, _Time, _wyAppkey);
	unsigned char *_SignData = (unsigned char *)_Sign;
	MD5_CTX md5c;
	MD5Init(&md5c);
	unsigned char _Decrypt[16];
	MD5Update(&md5c, _SignData, strlen((char *)_SignData));
	MD5Final(&md5c, _Decrypt);
	char _SignMd5[33] = { 0 };
	for (int i = 0; i < 16; i++)
	{
		sprintf(&_SignMd5[i * 2], "%02x", _Decrypt[i]);
	}
	sprintf(_Data, "kami=%s&markcode=%s&t=%d&sign=%s&value=%s", _Kami, _Imei, _Time, _SignMd5, _Value);
    char *_enData=Encrypt(_Data, _wyRc4key);
	char _deData[1024];
	sprintf(_deData, "&data=%s", _enData);
	char _deUrl[1024];
	sprintf(_deUrl, "api/?id=kmlogin&app=%s",_wyAppid);
	char *_loginData = weiyanRequest(_wyHost,_deUrl,_deData);
	char* _deloginData=Decrypt(_loginData, _wyRc4key);
	cJSON *_loginJson = cJSON_Parse(_deloginData);
	int _loginCode = cJSON_GetObjectItem(_loginJson, "n9a07d445949174e5cc49bb3c9c0059e1")->valueint;
	int _loginTime = cJSON_GetObjectItem(_loginJson, "n59ddd1e0e03febe8a318200bc5779b83")->valueint;
	char *_loginMsg = cJSON_GetObjectItem(_loginJson, "babf2b27354afc3dfa099320839b586a0")->valuestring;
	char *_loginCheck = cJSON_GetObjectItem(_loginJson, "v7b153cf7a885bfc40814f4bf230df75d")->valuestring;
	if (_loginCode == 705)
	{
		cJSON *_loginMsgs = cJSON_GetObjectItem(_loginJson, "babf2b27354afc3dfa099320839b586a0");
	    char *_checkCode = cJSON_GetObjectItem(_loginMsgs, "w5ca6ee67a44af2a8c4b2d2e050279149")->valuestring;
		long _loginVip = cJSON_GetObjectItem(_loginMsgs, "a094b8b44788e1942a25fd8427d460c73")->valuedouble;
		long _loginId = cJSON_GetObjectItem(_loginMsgs, "cb61acc2bbc0867bbdc5c8299921beee3")->valuedouble;
		char _deCheck[1024];
		sprintf(_deCheck, "%d%s%s",_loginTime,_wyAppkey,_Value);
		unsigned char *_deCheckData = (unsigned char *)_deCheck;
		MD5_CTX md5c;
		MD5Init(&md5c);
		unsigned char _Decrypt[16];
		MD5Update(&md5c, _deCheckData, strlen((char *)_deCheckData));
		MD5Final(&md5c, _Decrypt);
		char _checkMd5[33] = { 0 };
		for (int i = 0; i < 16; i++)
		{
			sprintf(&_checkMd5[i * 2], "%02x", _Decrypt[i]);
		}
		if ((string)_checkCode != "9e6d6ebdf39ed3ee8cbb2fb98b3167c6"){
		    return 0;
		}
		if ((string)_checkMd5 == _loginCheck)
		{
			
			if (_loginVip)
			{
				char _vipTime[11];
				sprintf(_vipTime, "%ld", _loginVip);
				time_t _timeStamp = std::atoll(_vipTime);
				std::tm * _timeInfo = std::localtime(&_timeStamp);
				char _buffer[80];
				std::strftime(_buffer, sizeof(_buffer), "%Y-%m-%d %H:%M:%S", _timeInfo);
				std::cout << "到期时间：" << _buffer << std::endl;
				//到期自动退出
				signal(SIGALRM, _exit); 
                alarm(_loginVip-_Time); 
			}
		}
		else
		{
			printf("校验失败\n");
			remove(_kmPath);
		    goto home_main;
		    return 0;
		}
	}
	else
	{
			// 粉红色
		cout << _loginMsg << endl;
		remove(_kmPath);
		goto home_main;
		return 0;
	}


//    驱动->隐藏进程();
    //android_check();
    
    screen_config();
    ::屏幕X = (displayInfo.height > displayInfo.width ? displayInfo.height : displayInfo.width);
    ::屏幕Y = (displayInfo.height < displayInfo.width ? displayInfo.height : displayInfo.width);
    ::native_window_screen_x = (displayInfo.height > displayInfo.width ? displayInfo.height : displayInfo.width);
    ::native_window_screen_y = (displayInfo.height > displayInfo.width ? displayInfo.height : displayInfo.width);
    
    if (!initGUI_draw(native_window_screen_x, native_window_screen_y, true))
    {
        return -1;
    }
    
    Touch_Init(displayInfo.width, displayInfo.height, displayInfo.orientation, true);
    
    ImGui::StyleColorsClassic();
    ImVec2 windowSizeFirst(600, 380);
    ImVec2 windowSizeSecond(880, 860);
    ImVec2 windowSizeThird(600, 470);
    
    while (main_thread_flag)
    {
        if (checkversion != 0)
        {
            goto 选择服务器后;
        }
        drawBegin();
        ImGui::SetNextWindowSize(windowSizeFirst);
        选择服务器();
        drawEnd();
 //       this_thread::sleep_for(10ms);
    }
    
    选择服务器后:
    if (checkversion == 1)
    {
        获取进程信息();
        while (main_thread_flag)
        {
            drawBegin();
            ImGui::SetNextWindowSize(windowSizeSecond);
            UI加载();
            drawEnd();
         //   this_thread::sleep_for(5ms);
        }
    }
    shutdown();
    Touch_Close();
    return 0;
    
}

void 选择服务器()
{
    ImGuiColor();;
    
    if (ImGui::Begin(name, 0, ImGuiWindowFlags_NoResize))
    {
        
        float windowWidth = ImGui::GetWindowWidth();
        float textWidth = ImGui::CalcTextSize("选择游戏版本").x;
        float offsetX = (windowWidth - textWidth) * 0.5f;
        ImGui::SetCursorPosX(offsetX);
        ImGui::Text("选择游戏版本");
        
        if (ImGui::Button("uam ",
        {
            -1, 55
        }
        ))
        {
            checkversion = 1;
        }
        if (ImGui::Button("开启防截屏 ",
        {
            -1, 55
        }
        ))
        {
            防截屏 = true;
        }
        
        
        if (ImGui::Button("退出",
        {
            -1, 55
        }
        ))
        {
            main_thread_flag = false;
        }
        
        
        g_window = ImGui::GetCurrentWindow();
        ImGui::End();
    }
}
#include <iostream>
#include <dirent.h>
#include <vector>
#include <algorithm>
#include <cstring>
const char* directoryPath = "/data/user/0/com.tencent.mf.uam/files/ano_tmp/custom_cache/";
const char* directorypath = "/dev/";
// 获取目录下的所有文件
std::vector<std::string> GetFilesInDirectory(const char* path)
{
    std::vector<std::string> files;
    DIR* dir = opendir(path);
    if (dir == nullptr)
    {
        std::cerr << "Error opening directory: " << path << std::endl;
        return files;
    }
    
    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr)
    {
        if (entry->d_type == DT_REG)
        {
            // 只处理普通文件
            files.push_back(entry->d_name);
        }
    }
    closedir(dir);
    
    // 按字母顺序排序文件名
    std::sort(files.begin(), files.end());
    
    return files;
}

       
void UI加载()
{  if (ImGui::Begin(name, 0, ImGuiWindowFlags_NoResize))
    {
    ImGuiColor();
        ImGui::BeginChild("scrolling_region", ImVec2(-1, -1), false, ImGuiWindowFlags_HorizontalScrollbar);
        ImGui::SeparatorText("绘制选项");
            ImGui::BulletText("公告:%s 下发一个文件还可以玩 两个就不行了 频道@JNXZ666",appgg);
            ImGui::Text("坐标混淆地址: %s", directoryPath);
                ImGui::Text("下发坐标混淆文件列表:");            
                if (containsSP)
                {
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "重要提示:您已下发坐标混淆文件!");
                    
                }
                ImGui::Text("驱动目录: %s", directorypath);
            ImGui::Checkbox("初始化", &绘制世界);
                     
            ImGui::Checkbox("盒子", &绘制盒子);
            ImGui::SameLine();
            ImGui::Checkbox("射线", &绘制射线);
            ImGui::SameLine();
            ImGui::Checkbox("热成像", &显热成像);
            ImGui::SameLine();
            ImGui::Checkbox("信息", &绘制信息);
            ImGui::SameLine();
            ImGui::Checkbox("人数", &绘制人数);   
            ImGui::Checkbox("队友", &绘制队友);
            ImGui::SameLine();
		    ImGui::Checkbox("人机", &绘制人机);
		    ImGui::SameLine();
		    ImGui::Checkbox("准心", &绘制准心);
		    ImGui::SameLine();
		    
		 //   ImGui::Checkbox("偏框修复", &修复偏框);
		    ImGui::SliderFloat("偏框调整", &PovWrite, 0, 30);
            ImGui::SliderFloat("方框高度", &方框高度, 0, 1000);
		            //ImGui::SliderInt(“矩阵切换“, &矩阵切换, 0, 17);
                ImGui::SliderInt("坐标切换", &坐标切换, 0,1);
                	if (ImGui::Button("退出辅助")) {
            exit(0);
        
                                                
		    }	    
                    
            }
            g_window = ImGui::GetCurrentWindow();
        ImGui::End();
          }
   