#include <arpa/inet.h>
#include <cctype>
#include <chrono>
#include <codecvt>
#include <cstdlib>
#include <ctime>
#include <deque>
#include <dirent.h>
#include <dlfcn.h>
#include <errno.h>
#include <fcntl.h>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <locale>
#include <malloc.h>
#include <map>
#include <math.h>
#include <netdb.h>
#include <netinet/in.h>
#include <pthread.h>
#include <sstream>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/uio.h>
#include <thread>
#include <unistd.h>
#include <vector>

const int 贪吃小蛇格子大小 = 20;
const int 贪吃小蛇宽度 = 20;
const int 贪吃小蛇高度 = 20;
enum class 贪吃小蛇方向 { 上, 下, 左, 右 };
class 贪吃小蛇 {
public:
贪吃小蛇() {
重置();
}
void 重置() {
身体.clear();
int 初始方向随机数 = rand() % 4;
if (初始方向随机数 == 0) {
方向 = 贪吃小蛇方向::上;
} else if (初始方向随机数 == 1) {
方向 = 贪吃小蛇方向::下;
} else if (初始方向随机数 == 2) {
方向 = 贪吃小蛇方向::左;
} else if (初始方向随机数 == 3) {
方向 = 贪吃小蛇方向::右;
}
int 初始位置随机数_x = rand() % 20;
int 初始位置随机数_y = rand() % 20;
身体.push_back({初始位置随机数_x, 初始位置随机数_y});
生成食物();
分数 = 0;
游戏结束 = false;
上次移动时间 = chrono::steady_clock::now();
}
void 移动() {
if (!游戏结束) {
auto 当前时间 = chrono::steady_clock::now();
auto 经过时间 = chrono::duration_cast<chrono::milliseconds>(当前时间 - 上次移动时间).count();
if (经过时间 >= 移动间隔) {
上次移动时间 = 当前时间;
auto 头部 = 身体.front();
if (方向 == 贪吃小蛇方向::上) {
头部.y--;
} else if (方向 == 贪吃小蛇方向::下) {
头部.y++;
} else if (方向 == 贪吃小蛇方向::左) {
头部.x--;
} else if (方向 == 贪吃小蛇方向::右) {
头部.x++;
}
if (头部.x < 0 || 头部.x >= 贪吃小蛇宽度 || 头部.y < 0 || 头部.y >= 贪吃小蛇高度) {
游戏结束 = true;
}
for (auto it = 身体.begin() + 1; it != 身体.end(); ++it) {
if (头部 == *it) {
游戏结束 = true;
break;
}
}
if (!游戏结束) {
身体.push_front(头部);
if (头部 == 食物) {
分数++;
生成食物();
} else {
身体.pop_back();
}
}
}
}
}
void 生成食物() {
bool 有效 = false;
while (!有效) {
食物.x = rand() % 贪吃小蛇宽度;
食物.y = rand() % 贪吃小蛇高度;
有效 = true;
for (auto &节 : 身体) {
if (节 == 食物) {
有效 = false;
break;
}
}
}
}
void 设置方向(贪吃小蛇方向 dir) {
方向 = dir;
}
贪吃小蛇方向 获取方向() {
return 方向;
}
bool 游戏是否结束() {
return 游戏结束;
}
void 贪吃小蛇游戏绘制() {
ImGui::Begin("贪吃小蛇游戏", nullptr, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize);
ImVec2 游戏区域位置 = ImGui::GetCursorScreenPos();
ImVec2 单元格大小 = ImVec2(21, 21); 
ImVec2 游戏区域大小 = ImVec2(贪吃小蛇宽度 * 单元格大小.x, 贪吃小蛇高度 * 单元格大小.y);
ImVec4 颜色;
ImDrawList* 绘制列表 = ImGui::GetWindowDrawList();
for (int y = 0; y < 贪吃小蛇高度; ++y) {
for (int x = 0; x < 贪吃小蛇宽度; ++x) {
if (x == 食物.x && y == 食物.y) {
颜色 = ImVec4(1.0f, 0.0f, 0.0f, 1.0f); 
} else {
bool 是蛇身 = false;
for (auto& 节 : 身体) {
if (节.x == x && 节.y == y) {
颜色 = (节 == 身体.front()) ? ImVec4(1.0f, 1.0f, 0.0f, 1.0f) : ImVec4(0.0f, 1.0f, 0.0f, 1.0f); 
是蛇身 = true;
break;
}
}
if (!是蛇身) {
颜色 = ImVec4(1.0f, 1.0f, 1.0f, 1.0f); 
}
}
ImVec2 左上角 = ImVec2(游戏区域位置.x + x * 单元格大小.x, 游戏区域位置.y + y * 单元格大小.y);
ImVec2 右下角 = ImVec2(游戏区域位置.x + (x + 1) * 单元格大小.x, 游戏区域位置.y + (y + 1) * 单元格大小.y);
绘制列表->AddRectFilled(左上角, 右下角, ImGui::ColorConvertFloat4ToU32(颜色));
绘制列表->AddLine(左上角, ImVec2(右下角.x, 左上角.y), IM_COL32(200, 200, 200, 200));
绘制列表->AddLine(左上角, ImVec2(左上角.x, 右下角.y), IM_COL32(200, 200, 200, 200));
绘制列表->AddLine(ImVec2(右下角.x, 左上角.y), 右下角, IM_COL32(200, 200, 200, 200));
绘制列表->AddLine(ImVec2(左上角.x, 右下角.y), 右下角, IM_COL32(200, 200, 200, 200));
if (x == 身体.front().x && y == 身体.front().y) {
ImVec2 黑点中心 = ImVec2(左上角.x + 单元格大小.x / 2, 左上角.y + 单元格大小.y / 2);
if (方向 == 贪吃小蛇方向::上) {
黑点中心.y -= 8.0f;
} else if (方向 == 贪吃小蛇方向::下) {
黑点中心.y += 8.0f;
} else if (方向 == 贪吃小蛇方向::左) {
黑点中心.x -= 8.0f;
} else if (方向 == 贪吃小蛇方向::右) {
黑点中心.x += 8.0f;
}
绘制列表->AddCircleFilled(黑点中心, 2.0f, IM_COL32(0, 0, 0, 255), 4);
}
ImGui::SameLine();
}
ImGui::NewLine();
}
ImVec2 空白区域位置 = ImGui::GetCursorScreenPos();
ImGui::SetCursorScreenPos(ImVec2(游戏区域位置.x, 游戏区域位置.y + 游戏区域大小.y));
float 得分文本宽度 = ImGui::CalcTextSize("得分：").x;
float 得分文本位置X = 游戏区域位置.x + (游戏区域大小.x - 得分文本宽度) / 2;
ImGui::SetCursorScreenPos(ImVec2(得分文本位置X, ImGui::GetCursorScreenPos().y));
ImGui::Text("得分:%d", 分数);
if (!游戏结束) {
if (ImGui::Button("上", ImVec2(418, 55))) {
方向选中[0] = true;
方向选中[1] = false;
方向选中[2] = false;
方向选中[3] = false;
方向 = 贪吃小蛇方向::上;
}
if (ImGui::Button("左", ImVec2(201, 55))) {
方向选中[0] = false;
方向选中[1] = false;
方向选中[2] = true;
方向选中[3] = false;
方向 = 贪吃小蛇方向::左;
}
ImGui::SameLine();
if (ImGui::Button("右", ImVec2(201, 55))) {
方向选中[0] = false;
方向选中[1] = false;
方向选中[2] = false;
方向选中[3] = true;
方向 = 贪吃小蛇方向::右;
}
if (ImGui::Button("下", ImVec2(418, 55))) {
方向选中[0] = false;
方向选中[1] = true;
方向选中[2] = false;
方向选中[3] = false;
方向 = 贪吃小蛇方向::下;
}
if (ImGui::Button("重置", ImVec2(-1, 55))) {
重置();
}
} else {
float 游戏结束文本宽度 = ImGui::CalcTextSize("游戏结束").x;
float 游戏结束文本位置X = 游戏区域位置.x + (游戏区域大小.x - 游戏结束文本宽度) / 2;
ImGui::SetCursorScreenPos(ImVec2(游戏结束文本位置X, ImGui::GetCursorScreenPos().y));
ImGui::Text("游戏结束");
if (ImGui::Button("重置", ImVec2(-1, 55))) {
重置();
}
}
ImGui::End();
}
private:
struct 贪吃小蛇食物 {
int x, y;
bool operator==(const 贪吃小蛇食物 &other) const {
return x == other.x && y == other.y;
}
};
deque<贪吃小蛇食物> 身体;
贪吃小蛇食物 食物;
贪吃小蛇方向 方向;
int 分数 = 0;
bool 游戏结束 = false;
chrono::steady_clock::time_point 上次移动时间;
const int 移动间隔 = 300;
bool 方向选中[4] = {false};
};
贪吃小蛇 贪吃小蛇游戏;
void 贪吃小蛇游戏循环() {
贪吃小蛇游戏.移动();
}
void 贪吃小蛇游戏绘制() {
贪吃小蛇游戏.贪吃小蛇游戏绘制();
}