#include "Android_draw/draw.h"
#include "main.h"
#include <iostream>
#include <fstream>
#include <string>
#include <cstdlib>
#include <ctime>

EGLDisplay display = EGL_NO_DISPLAY;
EGLConfig config;
EGLSurface surface = EGL_NO_SURFACE;
EGLContext context = EGL_NO_CONTEXT;
bool 防截屏;

ANativeWindow *native_window;
int native_window_screen_x = 0;
int native_window_screen_y = 0;
android::ANativeWindowCreator::DisplayInfo displayInfo{0};
uint32_t orientation = 0;
bool g_Initialized = false;
ImGuiWindow *g_window = nullptr;

bool initGUI_draw(uint32_t _screen_x, uint32_t _screen_y, bool log) {
orientation = displayInfo.orientation;
#if defined(USE_OPENGL)
if (!init_egl(_screen_x, _screen_y, log)) {
return false;
}
#else
InitVulkan();
SetupVulkan();
::native_window = android::ANativeWindowCreator::Create("CosmicClassic", _screen_x, _screen_y, 防截屏);
SetupVulkanWindow(::native_window, (int) _screen_x, (int) _screen_y);
#endif
if (!ImGui_init()) {
return false;
}
#ifndef USE_OPENGL
UploadFonts();
#endif
return true;
}

bool init_egl(uint32_t _screen_x, uint32_t _screen_y, bool log) {
::native_window = android::ANativeWindowCreator::Create("CosmicClassic", _screen_x, _screen_y, 防截屏);

ANativeWindow_acquire(native_window);
display = eglGetDisplay(EGL_DEFAULT_DISPLAY);
EGLint num_config = 0;
const EGLint attribList[] = {
EGL_SURFACE_TYPE, EGL_WINDOW_BIT,
EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT,
EGL_BLUE_SIZE, 5,
EGL_GREEN_SIZE, 6,
EGL_RED_SIZE, 5,
EGL_BUFFER_SIZE, 32,
EGL_DEPTH_SIZE, 16,
EGL_STENCIL_SIZE, 8,
EGL_NONE
};
const EGLint attrib_list[] = {
EGL_CONTEXT_CLIENT_VERSION,
3,
EGL_NONE
};
EGLint egl_format;
eglGetConfigAttrib(display, config, EGL_NATIVE_VISUAL_ID, &egl_format);
ANativeWindow_setBuffersGeometry(native_window, 0, 0, egl_format);
context = eglCreateContext(display, config, EGL_NO_CONTEXT, attrib_list);
surface = eglCreateWindowSurface(display, config, native_window, nullptr);
return true;
}

void screen_config() {
displayInfo = android::ANativeWindowCreator::GetDisplayInfo();
}

bool ImGui_init() {
if (g_Initialized) {
return true;
}
IMGUI_CHECKVERSION();
ImGui::CreateContext();
ImGui::StyleColorsDark();
ImGui_ImplAndroid_Init(native_window);
#if defined(USE_OPENGL)
ImGui_ImplOpenGL3_Init("#version 300 es");
#endif
ImGuiIO &io = ImGui::GetIO();
io.IniFilename = NULL;
static ImFontConfig font_cfg;
font_cfg.SizePixels = 45.0f;
io.Fonts->AddFontFromFileTTF("./font.ttf", 45.0f , NULL, io.Fonts->GetGlyphRangesChineseFull());
const std::string filename = "./font.ttf";
std::ifstream file(filename);
if (!file.is_open()) {
std::cout << "未找到font.ttf" << std::endl;
exit(0);
}
io.Fonts->AddFontDefault(&font_cfg);
ImGui::GetStyle().ScaleAllSizes(2.0f);
::g_Initialized = true;
return true;
}

void drawBegin() {
screen_config();
if (::orientation != displayInfo.orientation) {
::orientation = displayInfo.orientation;
UpdateScreenData(displayInfo.width, displayInfo.height, displayInfo.orientation);
g_window->Pos.x = 100;
g_window->Pos.y = 100;
}

#ifdef USE_OPENGL
ImGui_ImplOpenGL3_NewFrame();
#else
ImGui_ImplVulkan_NewFrame();
#endif
ImGui_ImplAndroid_NewFrame(native_window_screen_x, native_window_screen_y);
ImGui::NewFrame();
}

void drawEnd() {
ImGui::Render();

#ifdef USE_OPENGL
glClear(GL_COLOR_BUFFER_BIT);
ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
eglSwapBuffers(display, surface);
#else
FrameRender(ImGui::GetDrawData());
FramePresent();
#endif
}

void shutdown() {
if (!g_Initialized) {
return;
}
#ifdef USE_OPENGL
ImGui_ImplOpenGL3_Shutdown();
#else
DeviceWait();
ImGui_ImplVulkan_Shutdown();
#endif
ImGui_ImplAndroid_Shutdown();
ImGui::DestroyContext();

#ifdef USE_OPENGL
if (display != EGL_NO_DISPLAY) {
eglMakeCurrent(display, EGL_NO_SURFACE, EGL_NO_SURFACE, EGL_NO_CONTEXT);
if (context != EGL_NO_CONTEXT) {
eglDestroyContext(display, context);
}
if (surface != EGL_NO_SURFACE) {
eglDestroySurface(display, surface);
}
eglTerminate(display);
}
display = EGL_NO_DISPLAY;
context = EGL_NO_CONTEXT;
surface = EGL_NO_SURFACE;
#else
CleanupVulkanWindow();
CleanupVulkan();
#endif

ANativeWindow_release(native_window);
android::ANativeWindowCreator::Destroy(native_window);
::g_Initialized = false;
}